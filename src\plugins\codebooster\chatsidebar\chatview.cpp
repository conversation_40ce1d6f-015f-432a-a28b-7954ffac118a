#include "chatview.h"
#include "ui_chatview.h"

#include <QDebug>
#include <QNetworkRequest>
#include <QJsonDocument>
#include <QSpacerItem>
#include <QScrollBar>
#include <QToolButton>
#include <utils/stylehelper.h>
#include <QDebug>
#include <utils/utilsicons.h>

#include "chatexportdialog.h"
#include "codeboostericons.h"
#include "chathistorypage.h"
#include "instrumentor.h"
#include "widgettheme.h"
#include "codeboosterutils.h"
#include "llmiconfactory.h"
#include "asksuggestionwgt.h"
#include "useroptions.h"

namespace CodeBooster::Internal{

ChatView::ChatView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ChatView)
    , mCurAssistantMsgWgt(nullptr)
    , manager(new QNetworkAccessManager(this))
    , mRequestRunning(false)
    , mUserScrolledUpWhileStreaming(false)
    , previousScrollValue(0)
    , mHistoryPage(nullptr)
    , mAskSuggestionWgt(nullptr)
{
    ui->setupUi(this);

    //QPalette customPalette = this->palette();
    ChatViewManager::instance().registerView(this);

    // 初始化布局和样式
    {
        // 暗色模式下微调横线样式
        if (CB_SETTING.isDarkTheme())
        {
            ui->line_2->setMaximumHeight(1);
            ui->line_2->setMinimumHeight(1);
            ui->line_2->setStyleSheet("border: 1px solid #d0d0d0");
        }

        ui->scrollArea->setStyleSheet("QScrollArea{border: none;}");
        connect(ui->scrollArea->verticalScrollBar(), &QScrollBar::valueChanged, this, [this](int value){
            if ((previousScrollValue != 0) && mRequestRunning)
            {
                int maxValue = ui->scrollArea->verticalScrollBar()->maximum();
                if (value < previousScrollValue)
                {
                    if (previousScrollValue < maxValue)
                        mUserScrolledUpWhileStreaming = true;
                }
                else if (value == maxValue)
                {
                    mUserScrolledUpWhileStreaming = false;
                }
            }
            previousScrollValue = value;
        });

        mMsgLayout = new QVBoxLayout(ui->scrollAreaWidgetContents);
        mMsgLayout->setContentsMargins(6, 6, 6, 10);
    }

    // 初始化问题建议控件
    {
        mAskSuggestionWgt = new AskSuggestionWgt(this);
        connect(mAskSuggestionWgt, &AskSuggestionWgt::suggestionCreated, this, [=](){
            mRequestRunning = false;

            bool atBottom = false;
            QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
            if ((scrollBar->maximum() - scrollBar->value()) < 10)
            {
                atBottom = true;
            }
            if (atBottom)
            {
                // 滚动区域还没有变化，如果立即调整位置不生效
                QTimer::singleShot(50, [scrollBar]() {
                    scrollBar->setValue(scrollBar->maximum());
                });
            }
        });
        connect(mAskSuggestionWgt, &AskSuggestionWgt::suggestionClicked, this, [=](QString suggestionText){
            mAskSuggestionWgt->setInactive();
            mInputWidget->setText(suggestionText);
            mInputWidget->onSendButtonClicked();
        });
    }

    // 初始化错误信息标签
    {
        ui->label_errInfo->setVisible(false);
        ui->label_errInfo->setWordWrap(true);
        QString labelStyle(R"(
    QLabel {
        background-color: #FFCCCC;
    border: 1px solid red;
    padding: 5px;
        border-radius: 6px;
    }
)");
        ui->label_errInfo->setStyleSheet(labelStyle);
    }

    // 初始化输入控件
    {
        mInputWidget = new InputWidget(this);
        connect(mInputWidget, &InputWidget::sendUserMessage, this, &ChatView::onSendUserMessage);
        connect(mInputWidget, &InputWidget::sendUserMessage, this, &ChatView::toSendUserMessage);
        connect(mInputWidget, &InputWidget::createNewChat, this, [this](){
            newChat();
        });
        connect(mInputWidget, &InputWidget::inputFocusModeChanged, this, [this](bool enable){
            int inputStretch = enable ? 3 : 0;
            ui->verticalLayout->setStretch(2, inputStretch);
        });

        {
            QVBoxLayout *inputLayout = new QVBoxLayout();
            inputLayout->addWidget(mInputWidget);
            inputLayout->setContentsMargins(4, 6, 4, 6);
            ui->verticalLayout->insertLayout(2, inputLayout);
            ui->verticalLayout->setStretch(0, 1);
        }

        ui->pushButton_newSession->setMaximumWidth(70);
        ui->pushButton_newSession->setIcon(NEW_ICON.icon());
        connect(ui->pushButton_newSession, &QPushButton::clicked, this, &ChatView::newChat);
        connect(ui->pushButton_export, &QPushButton::clicked, this, &ChatView::onExportBtnClicked);
        // 默认不显示输入控件下方导出按钮，导出功能通过工具栏触发
        ui->pushButton_export->setVisible(false);
    }

    // 初始化发送和停止按钮
    {
        ui->pushButton_send->setFixedWidth(52);
        ui->pushButton_send->setIcon(Utils::Icons::RUN_SMALL.icon());
        connect(ui->pushButton_send, &QPushButton::clicked, mInputWidget, &InputWidget::onSendButtonClicked);

        mStopGenerateButton = new QPushButton(ui->scrollArea);
        mStopGenerateButton->setText("终止 Crtl+Back");
        mStopGenerateButton->setFixedSize(128, 24);
        mStopGenerateButton->setIcon(ICON_STOP());
        mStopGenerateButton->setVisible(false);
        mStopGenerateButton->setCursor(Qt::PointingHandCursor);
        connect(mStopGenerateButton, &QPushButton::clicked, this, [=](){stopStreaming(false);});
        ui->scrollArea->installEventFilter(this);
    }

    // 初始化模型下拉框
    {
        // 加载模型配置信息
        loadModelSettings();
        connect(&CodeBoosterSettings::instance(), &CodeBoosterSettings::modelConfigUpdated, this, &ChatView::loadModelSettings);

        ui->comboBox_model->setMaximumWidth(250);
        ui->comboBox_model->setMinimumHeight(22);

        ui->comboBox_model->setCurrentText(USER_OP.sideBarChatModelTitle);

        connect(ui->comboBox_model, &QComboBox::currentTextChanged, this, [this](QString text) {
            ui->comboBox_model->setToolTip(ui->comboBox_model->currentText());
            USER_OP.sideBarChatModelTitle = text;
        });
    }

    ui->pushButton_backToChat->setIcon(BACK_ICON.icon());
    connect(ui->pushButton_backToChat, &QPushButton::clicked, this, &ChatView::onBtnBackToChatClicked);

    // 初始化动作
    {
        mActionHistory = new QAction(HISTORY_ICON.icon(), "对话历史", this);
        mActionHistory->setCheckable(false);
        connect(mActionHistory, &QAction::triggered, this, &ChatView::onActionHistoryTriggered);

        mActionShowEditorSelection = new QAction(Utils::Icons::LINK_TOOLBAR.icon(), "同步编辑器选中文本", this);
        mActionShowEditorSelection->setCheckable(true);
        mActionShowEditorSelection->setChecked(USER_OP.showEditorSelection);
        connect(mActionShowEditorSelection, &QAction::triggered, this, [this](){
            bool show = mActionShowEditorSelection->isChecked();
            USER_OP.showEditorSelection = show;
            mInputWidget->setShowEditorSelection(show);
        });

        mActionExport = new QAction(Utils::Icons::EXPORTFILE_TOOLBAR.icon(), "导出当前对话", this);
        mActionExport->setCheckable(false);
        connect(mActionExport, &QAction::triggered, this, &ChatView::onExportBtnClicked);
    }

    // 默认显示对话页
    ui->stackedWidget->setCurrentWidget(ui->page_chat);

    // 设置外观样式
    setupTheme();

    // 创建一个新对话
    newChat();

    // 初始化停止对话按钮位置
    updateStopButtonPos();
}

ChatView::~ChatView()
{
    qDebug() << "------" << Q_FUNC_INFO;
    ChatViewManager::instance().unregisterView(this);
    delete ui;
}

void ChatView::newChat()
{
    if (mRequestRunning)
    {
        return;
    }

    // 创建对话
    mCurSession = ChatSession();

    // 移除当前的消息控件
    mMessageWgts.clear();
    clearLayout(mMsgLayout);

    // 隐藏问题建议
    mAskSuggestionWgt->setInactive();
}

void ChatView::loadChat(const QString &uuid)
{
    showChatPage();

    if (uuid == mCurSession.uuid())
        return;

    ChatSession session;
    if (!ChatDatabase::instance().loadSessionByUuid(uuid, session))
    {
        showErrInfo({"加载对话失败：", QString("uuid: %1").arg(uuid), ChatDatabase::instance().lastError()});
        return;
    }

    newChat();

    mCurSession = session;

    // 加载并显示历史消息
    for (int index = 0; index < session.chatStorage().size(); index++)
    {
        QJsonObject obj = session.chatStorage().at(index).toObject();
        QString role = obj.value("role").toString();
        QString content = obj.value("content").toString();

        if (role == "user")
        {
            MessagePreviewWidget *wgt = newMessageWidget(MessagePreviewWidget::User);
            wgt->setUserMessage(content, session.getMessageContextsById(obj["id"].toString()));
        }
        else if (role == "assistant")
        {
            QString modelName = session.messageSource(index);
            MessagePreviewWidget *wgt = newMessageWidget(MessagePreviewWidget::Assistant, modelName);
            wgt->updatePreview(content);
        }
    }
}

QList<QToolButton *> ChatView::createToolButtons()
{
    auto historyButton = new QToolButton;
    historyButton->setDefaultAction(mActionHistory);
    historyButton->setProperty(Utils::StyleHelper::C_NO_ARROW, true);

    auto showEditorSelectionButton =  new QToolButton;
    showEditorSelectionButton->setDefaultAction(mActionShowEditorSelection);
    showEditorSelectionButton->setProperty(Utils::StyleHelper::C_NO_ARROW, true);

    auto exportChat =  new QToolButton;
    exportChat->setDefaultAction(mActionExport);
    exportChat->setProperty(Utils::StyleHelper::C_NO_ARROW, true);

    return {historyButton, showEditorSelectionButton, exportChat};
}

bool ChatView::event(QEvent *event)
{
    if (event->type() == QEvent::FocusIn)
    {
        mInputWidget->activateInput();
    }
    return QWidget::event(event);
}

void ChatView::keyPressEvent(QKeyEvent *event)
{
    if (event->modifiers() == Qt::ControlModifier)
    {
        if (event->key() == Qt::Key_N)
        {
            newChat();
            return;
        }
    }

    QWidget::keyPressEvent(event);
}

void ChatView::onSendUserMessage(const QString &message, const QList<ContextItem> &contexts)
{
    if (!canCreateNewRequest())
        return;

    // 隐藏错误消息
    ui->label_errInfo->setVisible(false);

    // 显示用户消息
    auto userMsgWgt = newMessageWidget(MessagePreviewWidget::User);
    userMsgWgt->setUserMessage(message, contexts);

    // 读取设置构建请求参数
    ModelParam param = currentModelParam();
    QJsonObject responseJson = CodeBoosterSettings::buildRequestParamJson(param, true);;
    QJsonArray messages = mCurSession.getChatMessage(message, contexts, CodeBoosterSettings::instance().chatAttachedMsgCount());
    responseJson.insert("messages", messages);

    // 发送请求
    request(responseJson, param);

    if (CodeBoosterSettings::instance().devMode())
    {
        CodeBooster::Internal::outputMessages({QString::fromLatin1(QJsonDocument(messages).toJson())});
        saveToTxtFile(QString::fromLatin1(QJsonDocument(messages).toJson()));
    }
}

void ChatView::sendUserMessageNoHistory(const QString &sysMsg,
                                        const QString &userMsg,
                                        const QList<ContextItem> &contexts,
                                        const QMap<QString, QVariant> &overrideParams)
{
    if (!canCreateNewRequest())
        return;

    // 覆写模型参数
    ModelParam param = currentModelParam();
    param.overrideParams(overrideParams);

    // 设置对话控件状态
    mInputWidget->waitingForReceiveMsg();

    // 显示用户消息
    auto userMsgWgt = newMessageWidget(MessagePreviewWidget::User);
    userMsgWgt->setUserMessage(userMsg, contexts);

    // 读取设置构建请求参数
    QJsonObject responseJson = CodeBoosterSettings::buildRequestParamJson(param, true);
    QJsonArray messages = mCurSession.getChatMessage(sysMsg, userMsg, contexts);
    responseJson.insert("messages", messages);

    // 发送请求
    request(responseJson, param);
}

bool ChatView::isFloating() const
{
    QWidget *parent = this->parentWidget();
    while (parent)
    {
        if (qobject_cast<QDialog*>(parent))
            return true;
        parent = parent->parentWidget();
    }

    return false;
}

bool ChatView::eventFilter(QObject *obj, QEvent *event)
{
    if (ui->scrollArea == obj)
    {
        if (event->type() == QEvent::Resize)
        {
            updateStopButtonPos();
            return false;
        }
    }

    return QObject::eventFilter(obj, event);
}

void ChatView::request(const QJsonObject &responseJson, const ModelParam &param)
{
    QJsonDocument doc(responseJson);
    QByteArray data_s = doc.toJson(QJsonDocument::JsonFormat::Compact);

    // 创建请求
    QString url = param.apiUrl ;
    QNetworkRequest request = QNetworkRequest(QUrl(url));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("Authorization", QString("Bearer %1").arg(param.apiKey).toUtf8());

    repl = manager->post(request, data_s);
    connect(repl, &QNetworkReply::readyRead, this, &ChatView::streamReceived);
    connect(repl, &QNetworkReply::finished, this, &ChatView::handleReplyError);

    // 设置超时定时器
    mTimeoutTimer.setSingleShot(true);
    connect(&mTimeoutTimer, &QTimer::timeout, this, &ChatView::requestTimeout);
    mTimeoutTimer.start(20000);

    // 设置请求状态
    requestBegin();

    // 保存
    saveChatSession();
}


void ChatView::onExportBtnClicked()
{
    ChatExportDialog dlg(mCurSession, this);
    dlg.exec();
}

void ChatView::onActionHistoryTriggered()
{
    PROFILE_FUNCTION();

    if (ui->stackedWidget->currentWidget() == ui->page_history)
        return;

    // 初始化对话历史页面
    if (!mHistoryPage)
    {
        mHistoryPage = new ChatHistoryPage(mCurSession.uuid(), this);
        connect(mHistoryPage, &ChatHistoryPage::loadSessionHistory, this, &ChatView::loadChat);
        connect(mHistoryPage, &ChatHistoryPage::chatDeleted, this, [=](const QString &uuid){
            if (uuid == mCurSession.uuid())
            {
                newChat();
            }
        });
        ui->verticalLayout_2->addWidget(mHistoryPage);
    }
    else
    {
        mHistoryPage->highlightSession(mCurSession.uuid());
    }

    ui->stackedWidget->setCurrentWidget(ui->page_history);
}

void ChatView::onBtnBackToChatClicked()
{
    showChatPage();
}

bool ChatView::canCreateNewRequest(bool showInfo)
{
    // 是否有模型参数
    ModelParam param = currentModelParam();
    if (param.title.isEmpty())
    {
        if (showInfo)
        {
            outputMessage("无法发送对话请求：请配置模型参数", Error);
        }
        return false;
    }

    if (mRequestRunning)
    {
        outputMessage("无法发送对话请求：当前对话请求进行中，请结束后再试", Normal);
        return false;
    }

    return true;
}

void ChatView::setupTheme()
{
    ui->scrollAreaWidgetContents->setStyleSheet(CB_THEME.SS_ChatBackground);
}

MessagePreviewWidget *ChatView::newMessageWidget(MessagePreviewWidget::MessageMode mode, QString modelName)
{
    if (modelName.isEmpty())
    {
        modelName = (mode == MessagePreviewWidget::Assistant) ? currentModelParam().modelName : QString();
    }

    MessagePreviewWidget *wgt = new MessagePreviewWidget(mode, modelName, this);
    mMessageWgts << wgt;
    mMsgLayout->insertWidget(mMsgLayout->count() - 2, wgt);
    return wgt;
}

void ChatView::updateAssistantMessage(const QString &content)
{
    if (!mCurAssistantMsgWgt)
    {
        mCurAssistantMsgWgt = newMessageWidget(MessagePreviewWidget::Assistant);
    }

    mCurAssistantMsgWgt->updatePreview(content);
    resp += content;

    // 接受消息时保持滚动条在最下方显示最新消息
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    //qDebug() << "scrollBar->isVisible()" << scrollBar->isVisible() << "mUserScrolledWhileStreaming" << mUserScrolledUpWhileStreaming;
    if (scrollBar->isVisible() && !mUserScrolledUpWhileStreaming)
    {
        scrollBar->setValue(scrollBar->maximum());
    }

    // // 将流式传输的结果写入文件
    // if (mLogStream)
    // {
    //     *mLogStream << content << "\n";
    //     mLogStream->flush();
    // }
}

void ChatView::handleReplyError()
{
    mTimeoutTimer.stop();

    // 对话采用流式传输，因此只需要进行错误处理
    if (repl->error() != QNetworkReply::NoError)
    {
        QStringList errInfos;
        errInfos << "请求错误：";

        // 获取HTTP状态码
        QVariant statusCode = repl->attribute(QNetworkRequest::HttpStatusCodeAttribute);
        if (statusCode.isValid()) {
            errInfos<< "HTTP status code：" + QString::number(statusCode.toInt());
        }

        errInfos << QString("Network error code: %1").arg(repl->error());
        errInfos << QString("Network error string: %1").arg(repl->errorString());

        showErrInfo(errInfos);
        stopStreaming(false);
    }
}

void ChatView::streamReceived()
{
    // 终止超时定时器
    mTimeoutTimer.stop();

    // 隐藏错误提示
    ui->label_errInfo->setVisible(false);

    QByteArray get;
    get = repl->readLine();
    while (get.length() != 0)
    {
        if (get == "\n")
        {
            get = repl->readLine();
            continue;
        }

        int index = get.indexOf('{');
        QByteArray j_data = get.mid(index);
        if (get == "data: [DONE]\n")
        {
            stopStreaming();
            break;
        }

        QJsonDocument doc = QJsonDocument::fromJson(j_data);
        if (doc.isEmpty())
        {
            get = repl->readLine();
            continue;
        }

        QJsonObject jo = doc.object();
        if (jo.contains("choices") && jo["choices"].isArray())
        {
            QJsonArray a = jo["choices"].toArray();

            if (!a.isEmpty() && a[0].isObject() && a[0].toObject().contains("delta"))
            {
                QJsonObject jjo = a[0].toObject()["delta"].toObject();
                if (jjo.contains("content"))
                {
                    QString s = jjo["content"].toString();
                    updateAssistantMessage(s);
                }
                get = repl->readLine();
            }
        }
        else
        {
            QString str = QString::fromLocal8Bit(get);
            showErrInfo({str});
            stopStreaming(false);
            break;
        }
    }
}

/**
 * @brief ChatView::stopStreaming
 * @param finished 请求是否自然结束
 */
void ChatView::stopStreaming(bool finished)
{
    // 重置请求和接收数据状态
    {
        repl->disconnect();
        repl->deleteLater();

        if (resp.length() > 0)
        {
            mCurSession.appendAssistantMessage(resp, currentModelParam().title);
            // 保存
            saveChatSession();
            resp.clear();
        }
    }

    requestFinished();

    // 当请求结束时进行处理
    if (finished)
    {
        // 获取问题建议
        if (CodeBoosterSettings::instance().predictQuestions())
        {
            mAskSuggestionWgt->generateSuggestions(mCurSession, currentModelParam());
            mAskSuggestionWgt->setActive();
        }
    }
}

void ChatView::requestTimeout()
{
    // 停止
    repl->abort();

    // 显示超时提示
    ModelParam param = currentModelParam();

    QStringList msgs;
    msgs << "请求超时，请检查网络参数：";
    msgs << "Title: " + param.title;
    msgs << "Model: " + param.modelName;
    msgs << "apiUrl: " + param.apiUrl;
    msgs << "apiKey: " + param.apiKey;

    showErrInfo(msgs);

    // 重置请求状态
    stopStreaming(false);
}

/**
 * @brief ChatView::requestBegin 网络请求开始
 */
void ChatView::requestBegin()
{
    mRequestRunning = true;

    ui->pushButton_send->setEnabled(false);
    mStopGenerateButton->setVisible(true);

    ui->pushButton_newSession->setEnabled(false);
    mActionHistory->setEnabled(false);

    mAskSuggestionWgt->setInactive();
}

/**
 * @brief ChatView::requestEnd 网络请求结束
 */
void ChatView::requestFinished()
{
    mRequestRunning = false;

    mInputWidget->messageReceiveFinished();
    mCurAssistantMsgWgt = nullptr;
    mUserScrolledUpWhileStreaming = false;

    ui->pushButton_send->setEnabled(true);
    mStopGenerateButton->setVisible(false);

    ui->pushButton_newSession->setEnabled(true);
    mActionHistory->setEnabled(true);  
}

void ChatView::loadModelSettings()
{
    // 记录当前选中模型的信息
    QString oldSelectedModelTitle = currentModelParam().title;

    // 清空选线
    ui->comboBox_model->clear();

    // 加载模型信息
    for (const auto &param : CodeBoosterSettings::instance().chatParams())
    {
        QIcon icon = llmIcon(param.modelName);
        ui->comboBox_model->addItem(icon, param.title, QVariant::fromValue(param));
    }

    // 还原之前选择的模型
    if (!oldSelectedModelTitle.isEmpty())
    {
        ui->comboBox_model->setCurrentText(oldSelectedModelTitle);
    }

    // 更新发送消息按钮状态
    if (currentModelParam().title.isEmpty())
    {
        ui->pushButton_send->setEnabled(false);
    }
    else
    {
        ui->pushButton_send->setEnabled(true);
    }
}

void ChatView::showErrInfo(QStringList errInfos) const
{
    if (errInfos.isEmpty()) return;
    qDebug() << Q_FUNC_INFO;
    qDebug() << errInfos;
    ui->label_errInfo->setVisible(true);
    QString htmlStr = QString("<b><font color='black'>%1</font></b><br>").arg(errInfos.takeFirst());
    for (const QString &err : errInfos)
    {
        htmlStr += QString("<font color='black'>%1</font><br>").arg(err);
    }
    // 去掉末尾换行
    htmlStr.chop(4);

    ui->label_errInfo->setText(htmlStr);
}

void ChatView::clearLayout(QLayout *layout)
{
    if (!layout) return;

    QLayoutItem* item;
    while ((item = layout->takeAt(0)) != nullptr)
    {  
        if (item->widget())
        {
            QWidget* widget = item->widget();
            if (widget == mAskSuggestionWgt)
                continue;

            widget->setParent(nullptr);
            delete widget;
        }
        else if (item->layout())
        {
            clearLayout(item->layout());
            delete item->layout();
        }
        else
        {
            delete item;
        }
    }

    // 在布局末尾加上弹簧
    mMsgLayout->addWidget(mAskSuggestionWgt);
    mMsgLayout->addStretch(1);
}

void ChatView::saveChatSession()
{
    QString err;
    if (!ChatDatabase::instance().saveChatSession(mCurSession, err))
    {
        showErrInfo({"保存对话失败：", err});
    }

    // 保存对话后清理历史页面
    clearHistoryPage();
}

void ChatView::showChatPage()
{
    ui->stackedWidget->setCurrentWidget(ui->page_chat);
}

void ChatView::clearHistoryPage()
{
    if (mHistoryPage)
    {
        mHistoryPage->deleteLater();
        mHistoryPage = nullptr;
    }
}

ModelParam ChatView::currentModelParam() const
{
    if (ui->comboBox_model->count() == 0)
        return ModelParam();

    ModelParam param = ui->comboBox_model->currentData().value<ModelParam>();
    return param;
}

void ChatView::updateStopButtonPos()
{
    QPushButton *button = mStopGenerateButton;

    int buttonWidth = button->width();
    int buttonHeight = button->height();
    int parentWidth = button->parentWidget()->width();
    int parentHeight = button->parentWidget()->height();

    int margin = 5;

    // 计算按钮的新位置，使其位于底部中间
    int x = (parentWidth - buttonWidth) / 2;
    int y = parentHeight - buttonHeight - margin;

    // 设置按钮的新位置
    button->move(x, y);
}

// -------------------------------------------------------------------------
// ChatViewManager
// -------------------------------------------------------------------------
ChatViewManager::ChatViewManager()
{

}

ChatViewManager &ChatViewManager::instance()
{
    static ChatViewManager instance;
    return instance;
}

ChatView *ChatViewManager::getOneView() const
{
    if (!mViews.isEmpty())
        return mViews.first();

    return nullptr;
}

void ChatViewManager::registerView(ChatView *view)
{
    if (!view) return;
    mViews << view;
}

void ChatViewManager::unregisterView(ChatView *view)
{
    if (mViews.contains(view))
    {
        mViews.removeAll(view);
    }
}

QList<ChatView *> ChatViewManager::allViews() const
{
    return mViews;
}


} // namespace
