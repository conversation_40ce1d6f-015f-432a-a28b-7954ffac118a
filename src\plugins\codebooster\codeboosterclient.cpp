#include <QTimer>
#include <QToolButton>
#include <QScrollBar>

#include <languageclient/languageclientinterface.h>
#include <languageclient/languageclientmanager.h>
#include <languageclient/languageclientsettings.h>

#include <coreplugin/actionmanager/actionmanager.h>
#include <coreplugin/editormanager/editormanager.h>

#include <projectexplorer/projectmanager.h>
#include <utils/filepath.h>
#include <texteditor/textdocumentlayout.h>
#include <languageserverprotocol/lsptypes.h>

#include "codeboosterclient.h"
#include "codeboosterconstants.h"
#include "codeboostersettings.h"
#include "codeboostersuggestion.h"
#include "codeboosterclientinterface.h"
#include "editorchatwindow.h"
#include "useroptions.h"

using namespace LanguageServerProtocol;
using namespace TextEditor;
using namespace Utils;
using namespace ProjectExplorer;
using namespace Core;

namespace CodeBooster::Internal {

CodeBoosterClient::CodeBoosterClient()
    : LanguageClient::Client(new CodeBoosterClientInterface())
{
    setName("CodeBooster");
    LanguageClient::LanguageFilter langFilter;

    langFilter.filePattern = {"*"};

    setSupportedLanguage(langFilter);
    start();

    auto openDoc = [this](IDocument *document) {
        if (auto *textDocument = qobject_cast<TextDocument *>(document))
            openDocument(textDocument);
    };

    connect(EditorManager::instance(), &EditorManager::documentOpened, this, openDoc);
    connect(EditorManager::instance(),
            &EditorManager::documentClosed,
            this,
            [this](IDocument *document) {
                if (auto textDocument = qobject_cast<TextDocument *>(document))
                    closeDocument(textDocument);
            });

    for (IDocument *doc : DocumentModel::openedDocuments())
        openDoc(doc);

    connect(Core::EditorManager::instance(), &Core::EditorManager::currentEditorChanged, this, &CodeBoosterClient::onCurrentEditorChanged);
}

CodeBoosterClient::~CodeBoosterClient()
{
    for (IEditor *editor : DocumentModel::editorsForOpenedDocuments()) {
        if (auto textEditor = qobject_cast<BaseTextEditor *>(editor))
            textEditor->editorWidget()->removeHoverHandler(&m_hoverHandler);
    }
}

void CodeBoosterClient::openDocument(TextDocument *document)
{
    auto project = ProjectManager::projectForFile(document->filePath());
    if (!isEnabled(project))
        return;

    Client::openDocument(document);
    connect(document,
            &TextDocument::contentsChangedWithPosition,
            this,
            [this, document](int position, int charsRemoved, int charsAdded) {
                Q_UNUSED(charsRemoved)

                if (!CodeBoosterSettings::instance().autoComplete())
                {
                    return;
                }

                // 当上次补全文本的长度和文本新增长度相同时任务接受了补全，此时不再触发补全
                int lastRepSize = USER_OP.getLastCompletionReplySize();
                // qDebug() << "++contentsChangedWithPosition" << "charsAdded" << charsAdded << "lastRepSize" << lastRepSize;

                if (lastRepSize != 0 && charsAdded != 0 && lastRepSize == charsAdded)
                {
                    return;
                }
                else
                {
                    // 该条件只能用来做一次判断，用过之后重置
                    USER_OP.setLastCompletionReplySize(0);
                }

                auto project = ProjectManager::projectForFile(document->filePath());
                if (!isEnabled(project))
                    return;

                auto textEditor = BaseTextEditor::currentTextEditor();
                if (!textEditor || textEditor->document() != document)
                    return;
                TextEditorWidget *widget = textEditor->editorWidget();
                if (widget->multiTextCursor().hasMultipleCursors())
                    return;
                const int cursorPosition = widget->textCursor().position();
                if (cursorPosition < position || cursorPosition > position + charsAdded)
                    return;
                scheduleRequest(widget);
            });
}

void CodeBoosterClient::scheduleRequest(TextEditorWidget *editor)
{
    cancelRunningRequest(editor);

    if (!m_scheduledRequests.contains(editor)) {
        auto timer = new QTimer(this);
        timer->setSingleShot(true);
        connect(timer, &QTimer::timeout, this, [this, editor]() {
            if (m_scheduledRequests[editor].cursorPosition == editor->textCursor().position())
                requestCompletions(editor);
        });
        connect(editor, &TextEditorWidget::destroyed, this, [this, editor]() {
            delete m_scheduledRequests.take(editor).timer;
            cancelRunningRequest(editor);
        });
        connect(editor, &TextEditorWidget::cursorPositionChanged, this, [this, editor] {
            cancelRunningRequest(editor);
        });
        m_scheduledRequests.insert(editor, {editor->textCursor().position(), timer});
    } else {
        m_scheduledRequests[editor].cursorPosition = editor->textCursor().position();
    }
    m_scheduledRequests[editor].timer->start(500);
}

void CodeBoosterClient::requestCompletions(TextEditorWidget *editor)
{
    auto project = ProjectManager::projectForFile(editor->textDocument()->filePath());

    if (!isEnabled(project))
        return;

    Utils::MultiTextCursor cursor = editor->multiTextCursor();
    if (cursor.hasMultipleCursors() || cursor.hasSelection() || editor->suggestionVisible())
        return;

    const Utils::FilePath filePath = editor->textDocument()->filePath();
    GetCompletionRequest request
        {
            {
                TextDocumentIdentifier(hostPathToServerUri(filePath)),
                documentVersion(filePath),
                Position(cursor.mainCursor()),
                editor->textDocument()->plainText(),
                editor->position(),
         GetCompletionParams::TabCompletion
            }
        };

    request.setResponseCallback([this, editor = QPointer<TextEditorWidget>(editor)](
                                    const GetCompletionRequest::Response &response) {
        QTC_ASSERT(editor, return);
        handleCompletions(response, editor);
    });
    m_runningRequests[editor] = request;
    sendMessage(request);
}

void CodeBoosterClient::handleCompletions(const GetCompletionRequest::Response &response,
                                      TextEditorWidget *editor)
{
    if (response.error())
        log(*response.error());

    int requestPosition = -1;
    if (const auto requestParams = m_runningRequests.take(editor).params())
        requestPosition = requestParams->position().toPositionInDocument(editor->document());

    const Utils::MultiTextCursor cursors = editor->multiTextCursor();
    if (cursors.hasMultipleCursors())
        return;

    if (cursors.hasSelection() || cursors.mainCursor().position() != requestPosition)
        return;

    if (const std::optional<GetCompletionResponse> result = response.result()) {
    auto isValidCompletion = [](const Completion &completion) {
            return completion.isValid() && !completion.text().trimmed().isEmpty();
        };
        QList<Completion> completions = Utils::filtered(result->completions().toListOrEmpty(),
                                                              isValidCompletion);

        // remove trailing whitespaces from the end of the completions
        for (Completion &completion : completions) {
            const LanguageServerProtocol::Range range = completion.range();
            if (range.start().line() != range.end().line())
                continue; // do not remove trailing whitespaces for multi-line replacements

            const QString completionText = completion.text();
            const int end = int(completionText.size()) - 1; // empty strings have been removed above
            int delta = 0;
            while (delta <= end && completionText[end - delta].isSpace())
                ++delta;

            if (delta > 0)
                completion.setText(completionText.chopped(delta));
        }
        if (completions.isEmpty())
            return;
        editor->insertSuggestion(
            std::make_unique<CodeBoosterSuggestion>(completions, editor->document()));
        editor->addHoverHandler(&m_hoverHandler);
    }
}

void CodeBoosterClient::cancelRunningRequest(TextEditor::TextEditorWidget *editor)
{
    auto it = m_runningRequests.find(editor);
    if (it == m_runningRequests.end())
        return;
    cancelRequest(it->id());
    m_runningRequests.erase(it);
}

bool CodeBoosterClient::canOpenProject(Project *project)
{
    return isEnabled(project);
}

bool CodeBoosterClient::isEnabled(Project *project)
{
    if (!project)
        return CodeBoosterSettings::instance().autoComplete();

    CodeBoosterProjectSettings settings(project);
    return settings.isEnabled();
}

void CodeBoosterClient::onCurrentEditorChanged(Core::IEditor *editor)
{
    auto textEditor = BaseTextEditor::currentTextEditor();
    if (!textEditor)
        return;

    TextEditorWidget *widget = textEditor->editorWidget();

    if (m_connectedEditors.contains(widget))
        return;

    connect(widget, &TextEditorWidget::cursorPositionChanged, this, [this, widget, textEditor](){
        if (USER_OP.showEditorSelection)
        {
            emit documentSelectionChanged(widget->textDocument()->filePath().fileName(), widget->selectedText());
        }
    });

    m_connectedEditors << widget;
}

void CodeBoosterClient::requestEditorChatCompletions(TextEditor::TextEditorWidget *editor)
{
    Utils::MultiTextCursor cursor = editor->multiTextCursor();
    if (cursor.hasMultipleCursors() || cursor.hasSelection() || editor->suggestionVisible())
        return;

    const Utils::FilePath filePath = editor->textDocument()->filePath();
    GetCompletionRequest request
        {
            {
                TextDocumentIdentifier(hostPathToServerUri(filePath)),
                documentVersion(filePath),
                EditorChat::instance()->lspPosition(),
                editor->textDocument()->plainText(),
                EditorChat::instance()->editorPosition(),
                GetCompletionParams::ChatCompletion
            }
        };

    cancelChatRunningRequest(editor);

    request.setResponseCallback([this, editor = QPointer<TextEditorWidget>(editor)](
                                    const GetCompletionRequest::Response &response) {
        QTC_ASSERT(editor, return);
        handleChatCompletions(response, editor);
    });
    m_runningChatRequests[editor] = request;
    sendMessage(request);
}


void CodeBoosterClient::handleChatCompletions(const GetCompletionRequest::Response &response,
                                          TextEditorWidget *editor)
{
    if (response.error())
        log(*response.error());

    int requestPosition = -1;
    if (const auto requestParams = m_runningChatRequests.take(editor).params())
        requestPosition = requestParams->position().toPositionInDocument(editor->document());

    const Utils::MultiTextCursor cursors = editor->multiTextCursor();
    if (cursors.hasMultipleCursors())
        return;

    if (cursors.hasSelection() || cursors.mainCursor().position() != requestPosition)
        return;

    if (const std::optional<GetCompletionResponse> result = response.result()) {
        auto isValidCompletion = [](const Completion &completion) {
            return completion.isValid() && !completion.text().trimmed().isEmpty();
        };
        QList<Completion> completions = Utils::filtered(result->completions().toListOrEmpty(),
                                                        isValidCompletion);

        // remove trailing whitespaces from the end of the completions
        for (Completion &completion : completions) {
            const LanguageServerProtocol::Range range = completion.range();
            if (range.start().line() != range.end().line())
                continue; // do not remove trailing whitespaces for multi-line replacements

            const QString completionText = completion.text();
            const int end = int(completionText.size()) - 1; // empty strings have been removed above
            int delta = 0;
            while (delta <= end && completionText[end - delta].isSpace())
                ++delta;

            if (delta > 0)
                completion.setText(completionText.chopped(delta));
        }
        if (completions.isEmpty())
            return;
        editor->insertSuggestion(
            std::make_unique<CodeBoosterSuggestion>(completions, editor->document()));
        editor->addHoverHandler(&m_hoverHandler);
    }
}

void CodeBoosterClient::cancelChatRunningRequest(TextEditor::TextEditorWidget *editor)
{
    auto it = m_runningChatRequests.find(editor);
    if (it == m_runningChatRequests.end())
        return;

    cancelRequest(it->id());
    m_runningChatRequests.erase(it);
}

} // namespace CodeBooster::Internal
