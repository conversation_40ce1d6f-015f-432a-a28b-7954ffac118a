#include "chatdatabase.h"

#include <QJsonObject>
#include <QDebug>
#include <QDir>
#include <QSqlQuery>
#include <QSqlError>
#include <QJsonDocument>

#include "codeboosterutils.h"
#include "instrumentor.h"


namespace CodeBooster::Internal{

QString ChatDatabase::sessionTableSql = R"(CREATE TABLE IF NOT EXISTS "sessions" (
    "uuid" TEXT NOT NULL UNIQUE,
    "title" TEXT,
    "messages" TEXT,
    "message_source" TEXT,
    "context" TEXT,
    "modified_time" INTEGER DEFAULT 1721828273,
    "delete_flag" INTEGER DEFAULT 0,
    PRIMARY KEY("uuid")
);)";

QString ChatDatabase::editorChatInstTableSql = R"(CREATE TABLE IF NOT EXISTS "instructions" (
    "id" INTEGER PRIMARY KEY,
    "instruction" TEXT NOT NULL,
    "created_time" INTEGER DEFAULT 1721828273
);)";


/**
 * @brief ChatDatabase::ChatDatabase
 */
ChatDatabase::ChatDatabase()
{
    // 加载或创建数据库文件
    QString folderPath = dataFolderPath();
    QString fileName = "sessions.db";
    QString fullPath = folderPath + "/" + fileName;

    // 打开或创建数据库文件
    mDb = QSqlDatabase::addDatabase("QSQLITE");
    mDb.setDatabaseName(fullPath);

    if (!mDb.open())
    {
        outputMessages({"Error: connection with database failed"}, Error);
    }
    else
    {
        outputMessages({"Database: connection ok"}, Sucess);

        // 检查是否需要创建 sessions 表
        {
            QSqlQuery query(mDb);
            if (!query.exec("SELECT 1 FROM sessions LIMIT 1"))
            {
                // 表不存在，创建表
                if (!query.exec(sessionTableSql))
                {
                    outputMessages({QString("Failed to create sessions table: %1").arg(query.lastError().text())}, Error);
                } else
                {
                    outputMessage("Sessions table created successfully", Sucess);
                }
            }
        }

        // 创建instructions表
        {
            QSqlQuery query(mDb);
            if (!query.exec("SELECT 1 FROM instructions LIMIT 1"))
            {
                // 表不存在，创建表
                if (!query.exec(editorChatInstTableSql))
                {
                    outputMessages({QString("Failed to create instructions table: %1").arg(query.lastError().text())}, Error);
                } else
                {
                    outputMessage("instructions table created successfully", Sucess);
                }
            }
        }
    }
}

ChatDatabase::~ChatDatabase()
{

}

ChatDatabase &ChatDatabase::instance()
{
    static ChatDatabase db;
    return db;
}

bool ChatDatabase::saveChatSession(const ChatSession &session, QString &err)
{
    // 生成 SQL 语句
    QString sql = "INSERT OR REPLACE INTO sessions (uuid, title, messages, message_source, context, modified_time, delete_flag) VALUES (:uuid, :title, :messages, :message_source, :context, :modified_time, :delete_flag);";

    // 执行 SQL 语句
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":uuid", session.mUuid);
    query.bindValue(":title", session.mTitle);

    // 将 mChatStorage 转换为 JSON 字符串并绑定
    QJsonDocument chatDoc(session.mChatStorage);
    QString messages = QString::fromUtf8(chatDoc.toJson(QJsonDocument::Compact));
    query.bindValue(":messages", messages);

    // 将 mMessageSourceNames 转换为 JSON 数组字符串并绑定
    QJsonArray sourceArray;
    for (const QString &source : session.mMessageSourceNames) {
        sourceArray.append(source);
    }
    QJsonDocument sourceDoc(sourceArray);
    QString messageSource = QString::fromUtf8(sourceDoc.toJson(QJsonDocument::Compact));
    query.bindValue(":message_source", messageSource);

    query.bindValue(":context", session.allContextToJsonString());

    query.bindValue(":modified_time", session.mModifiedTime);
    query.bindValue(":delete_flag", 0);

    if (query.exec()) {
        //qDebug() << "Session saved successfully.";
        return true;
    } else {
        err = QString("Failed to save session. Error: %1").arg(query.lastError().text());
        outputMessage(sql);
        qDebug() << sql;
        return false;
    }
}

bool ChatDatabase::deleteChatSession(const QString &uuid, QString &err) const
{
    QSqlQuery query(mDb);
    query.prepare("UPDATE sessions SET delete_flag = 1 WHERE uuid = :uuid");
    query.bindValue(":uuid", uuid);

    if (query.exec())
    {
        qDebug() << "Session marked as deleted for uuid:" << uuid;
        return true;
    }
    else
    {
        err = QString("Failed to mark session as deleted for uuid: %1 Error: %2").arg(uuid).arg((query.lastError().text()));
        return false;
    }
}

bool ChatDatabase::loadSessionByUuid(const QString &uuid, ChatSession &session)
{
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE uuid = :uuid");
    query.bindValue(":uuid", uuid);

    if (query.exec() && query.next())
    {
        session.mUuid  = uuid;
        session.mTitle = query.value("title").toString();
        session.mModifiedTime = query.value("modified_time").toUInt();

        // 解析 messages 字段
        QString messagesJson = query.value("messages").toString();
        QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
        if (messagesDoc.isArray()) {
            session.mChatStorage = messagesDoc.array();
        }

        // 解析 message_source 字段
        QString messageSourceJson = query.value("message_source").toString();
        QJsonDocument messageSourceDoc = QJsonDocument::fromJson(messageSourceJson.toUtf8());
        if (messageSourceDoc.isArray()) {
            QJsonArray sourceArray = messageSourceDoc.array();
            session.mMessageSourceNames.clear();
            for (const QJsonValue &value : sourceArray) {
                session.mMessageSourceNames.append(value.toString());
            }
        }

        // 解析 context 字段
        session.loadContextsFromJsonString(query.value("context").toString());

        return true;
    }
    else
    {
        mLastError = QString("Failed to load session by uuid: %1").arg(query.lastError().text());
        return false;
    }
}

bool ChatDatabase::deleteAllSessions()
{
    QSqlQuery query(mDb);
    QString updateQuery = "UPDATE sessions SET delete_flag = 1 WHERE delete_flag = 0;";

    if (!query.exec(updateQuery))
    {
        mLastError = QString("Error updating delete_flag: %1" ).arg(query.lastError().text());
        return false;
    }
    else
    {
        return true;
    }
}

QList<ChatSessionBrief> ChatDatabase::loadAllSessions() const
{
    QList<ChatSessionBrief> sessions;

    // 准备并执行查询所有会话的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE delete_flag = 0 ORDER BY modified_time DESC");

    if (query.exec()) {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }
    } else {
        qDebug() << "Failed to load all sessions. Error:" << query.lastError().text();
    }

    return sessions;
}

/**
 * @brief ChatDatabase::loadSessionsByIndexRange 加载modified_time降序排列后范围内的信息
 * @param start
 * @param end
 * @return
 */
bool ChatDatabase::loadSessionsByIndexRange(int start, int end, QList<ChatSessionBrief> &sessions)
{
    PROFILE_FUNCTION();

    if (start < 0 || end < 0)
        return false;

    if (start > end)
        return false;

    // 准备并执行查询指定范围内会话的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE delete_flag = 0 ORDER BY modified_time DESC LIMIT :limit OFFSET :offset");
    query.bindValue(":limit", end - start);
    query.bindValue(":offset", start);

    if (query.exec()) {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }

        return true;
    }
    else
    {
        mLastError = QString( "Failed to load sessions by index range. Error:" ).arg(query.lastError().text());
        return false;
    }
}

int ChatDatabase::sessionCount()
{
    int count = 0;

    // 准备并执行查询会话数量的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT COUNT(*) FROM sessions WHERE delete_flag = 0");

    if (query.exec() && query.next()) {
        count = query.value(0).toInt();
    } else {
        mLastError = QString("Failed to get session count. Error: %1").arg(query.lastError().text());
    }

    return count;
}

int ChatDatabase::searchResultCount(const QString &keyword)
{
    // 获取符合搜索条件的总结果数量
    QSqlQuery countQuery(mDb);
    countQuery.prepare("SELECT COUNT(*) FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword))");
    countQuery.bindValue(":keyword", QVariant("%" + keyword + "%"));

    if (!countQuery.exec() || !countQuery.next()) {
        mLastError = QString("Failed to get search result count. Error: %1").arg(countQuery.lastError().text());
        return -1;
    }

    return countQuery.value(0).toInt();
}

bool ChatDatabase::searchMessage(const QString &keyword, QStringList &uuids)
{
    QSqlQuery query(mDb);
    query.prepare("SELECT uuid FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword))");
    query.bindValue(":keyword", QVariant("%" + keyword + "%"));

    if (query.exec())
    {
        while (query.next())
        {
            uuids << query.value(0).toString();
        }
        return true;
    }
    else
    {
        mLastError = QString("Search query failed: %1").arg(query.lastError().text());
        return false;
    }
}

/**
 * @brief ChatDatabase::searchMessage
 * @param keyword   查询关键字
 * @param start 返回结果的索引开始位置
 * @param end   返回结果的索引结束位置
 * @param sessions 返回结果信息
 * @return
 */
bool ChatDatabase::searchMessage(const QString &keyword, int start, int end, QList<ChatSessionBrief> &sessions)
{
    PROFILE_FUNCTION();

    if (start < 0 || end < 0)
        return false;

    if (start > end)
        return false;

    // 准备并执行分页查询
    QSqlQuery query(mDb);
    query.prepare("SELECT uuid, title, modified_time, messages FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword)) ORDER BY modified_time DESC LIMIT :limit OFFSET :offset");
    query.bindValue(":keyword", QVariant("%" + keyword + "%"));
    query.bindValue(":limit", end - start);
    query.bindValue(":offset", start);

    if (query.exec())
    {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }

        return true;
    }
    else
    {
        mLastError = QString("Search query failed: %1").arg(query.lastError().text());
        return false;
    }
}

bool ChatDatabase::saveInstruction(const QString &inst)
{
    // 生成 SQL 语句
    QString sql = "INSERT INTO instructions (instruction, created_time) VALUES (:instruction, :created_time);";

    // 执行 SQL 语句
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":instruction", inst);
    query.bindValue(":created_time", QDateTime::currentDateTime().toSecsSinceEpoch());

    if (query.exec()) {
        qDebug() << "instruction saved successfully.";
        return true;
    } else {
        mLastError = QString("Failed to save instruction. Error: %1").arg(query.lastError().text());
        outputMessage(sql);
        qDebug() << sql;
        return false;
    }
}

QStringList ChatDatabase::latestUniqueInstructions(int count)
{
    QStringList result;

    // 生成 SQL 查询语句，按 created_time 降序排列，并限制结果数量
    QString sql = "SELECT DISTINCT instruction FROM instructions ORDER BY created_time DESC LIMIT :count;";

    // 执行 SQL 查询
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":count", count);

    if (query.exec()) {
        // 遍历查询结果，将 instruction 添加到 QStringList 中
        while (query.next()) {
            QString instruction = query.value("instruction").toString();
            result.append(instruction);
        }
    } else {
        // 查询失败，输出错误信息
        mLastError = QString("Failed to fetch latest unique instructions. Error: %1").arg(query.lastError().text());
        qDebug() << mLastError;
    }

    // 将{4, 3, 2, 1}反转为{1, 2, 3, 4}
    std::reverse(result.begin(), result.end());

    return result;
}

QString ChatDatabase::lastError() const
{
    return mLastError;
}

// -------------------------------------------------------------------------
// ChatSession
// -------------------------------------------------------------------------
static QMap<ChatSession::Role, QString> roleStringMap{
    {ChatSession::User,      "user"},
    {ChatSession::Assistant, "assistant"}
};

ChatSession::ChatSession(const QString &sysMsg) :
    mUuid(QUuid::createUuid().toString()),
    mModifiedTime(QDateTime::currentDateTime().toSecsSinceEpoch())
{
    // TODO: 系统prompt从设置中读取
    QString prompt;
    if (sysMsg.isEmpty())
    {
        prompt = "你是一个专业的计算机科学和软件工程专家，可以使用C++时优先使用C++回答问题，你拒绝回答你角色范围外的问题，必须使用中文回答。";
    }
    else
    {
        prompt = sysMsg;
    }

    QJsonObject data;
    mSysMsg.insert("role",  "system");
    mSysMsg.insert("content", prompt);
}

void ChatSession::setChatTitle(const QString &title)
{
    mTitle = title;
}

QJsonArray ChatSession::chatStorage() const
{
    return mChatStorage;
}

QString ChatSession::messageSource(int index) const
{
    if (index < mMessageSourceNames.size())
    {
        return mMessageSourceNames.at(index);
    }

    return "未知";
}

void ChatSession::appendUserMessage(const QString &msg, const QList<ContextItem> &contexts)
{
    if (mTitle.isEmpty())
    {
        setChatTitle(msg);
    }

    QJsonObject msgObj = makeContentObject(User, msg);
    QString id = msgObj["id"].toString();

    mChatStorage.append(msgObj);
    mMessageSourceNames << "user";
    if (!contexts.isEmpty())
    {
        mContexts.insert(id, contexts);
    }
    mModifiedTime = QDateTime::currentDateTime().toSecsSinceEpoch();
}

void ChatSession::appendAssistantMessage(const QString &msg, const QString &model)
{
    mChatStorage.append(makeContentObject(Assistant, msg));

    mMessageSourceNames << model;
    mModifiedTime = QDateTime::currentDateTime().toSecsSinceEpoch();
}

QJsonArray ChatSession::getChatMessage(int maxMessageCount, bool useSysMsg)
{
    if (maxMessageCount <= 0)
    {
        maxMessageCount = 1;
    }

    // 获取最大数量为maxMessageCount的消息
    QJsonArray messages;
    int msgCount = 0;
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonObject data = getMessageObjByFromIndex(index);
        messages.prepend(data);

        msgCount++;

        if (msgCount >= maxMessageCount)
            break;
    }

    if (useSysMsg)
        messages.prepend(systemMsg());

    return messages;
}

QJsonArray ChatSession::getChatMessage(const QString &msg, const QList<ContextItem> &contexts, int maxMessageCount)
{
    appendUserMessage(msg, contexts);

    return getChatMessage(maxMessageCount);
}

QJsonArray ChatSession::getChatMessage(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts)
{
    appendUserMessage(userMsg, contexts);

    // 获取最后一条信息
    QJsonArray messages = getChatMessage(1, false);

    // 插入系统信息
    {
        QJsonObject msgObj;
        msgObj.insert("role", "system");
        msgObj.insert("content", sysMsg);
        messages.append(msgObj);
        messages.prepend(msgObj);
    }

    return messages;
}

QList<ContextItem> ChatSession::getMessageContextsById(QString id)
{
    return mContexts.value(id, QList<ContextItem>());
}

QString ChatSession::getLastAssistantMessageContent() const
{
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonValue value = mChatStorage.at(index);
        if (value.isObject())
        {
            auto obj = value.toObject();
            if (obj["role"] == "assistant")
                return obj["content"].toString();
        }
    }

    return QString();
}

QString ChatSession::getLastUserMessageContent() const
{
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonValue value = mChatStorage.at(index);
        if (value.isObject())
        {
            auto obj = value.toObject();
            if (obj["role"] == "user")
                return obj["content"].toString();
        }
    }

    return QString();
}

QString ChatSession::readableTime() const
{
    // 将 mModifiedTime 转换为 QDateTime 对象
    QDateTime dateTime;
    dateTime.setSecsSinceEpoch(mModifiedTime);

    // 返回格式化的时间字符串
    return dateTime.toString("yyyy-MM-dd HH:mm:ss");
}

ChatSessionBrief ChatSession::toBrief() const
{
    ChatSessionBrief brief;
    brief.uuid = mUuid;
    brief.title = mTitle;
    brief.modifiedTime = mModifiedTime;
    brief.messageCount = mMessageSourceNames.size();

    return brief;
}

QString ChatSession::readableTime(int timeStamp)
{
    QDateTime dateTime;
    dateTime.setSecsSinceEpoch(timeStamp);
    // 返回格式化的时间字符串
    return dateTime.toString("yyyy-MM-dd HH:mm:ss");
}

QJsonObject ChatSession::systemMsg() const
{
    return mSysMsg;
}

QJsonObject ChatSession::makeContentObject(Role role, const QString &msg) const
{
    QJsonObject data;
    data.insert("role",    roleStringMap.value(role));
    data.insert("content", msg);
    data.insert("id", QUuid::createUuid().toString());
    return data;
}

QJsonObject ChatSession::getMessageObjByFromIndex(int index)
{
    if (index < 0 || index >= mChatStorage.size())
        return QJsonObject();

    QJsonObject data = mChatStorage.at(index).toObject();

    // 添加上下文信息
    {
        QString id = data["id"].toString();
        QList<ContextItem> contextItems = mContexts.value(id, QList<ContextItem>());
        if (!contextItems.isEmpty())
        {
            QString contextString;
            contextString += "<CONTEXT>\n";
            for (ContextItem item : contextItems)
            {
                contextString += item.contextText() + "\n";
            }
            contextString += "</CONTEXT>\n";

            QString content = data["content"].toString();
            content = contextString + content;

            data["content"] = content;
        }
    }

    // 不发送id信息
    data.remove("id");

    return data;
}

QString ChatSession::allContextToJsonString() const
{
    QJsonArray contextsArray;
    for (QString id : mContexts.keys())
    {
        QJsonObject obj;

        {
            obj.insert("id", id);
        }

        {
            QJsonArray contextItemArray;
            for (auto ci : mContexts.value(id))
            {
                contextItemArray.append(ci.toJson());
            }

            obj.insert("contexts", contextItemArray);
        }

        contextsArray << obj;
    }

    /*
    [
        {
            "id": "msgUuid",
            "contexts":
            [
                {
                    "name": "main.cpp",
                    "description": "project/main.cpp",
                    "content": "#include<....",
                    "type": "1",
                    "uri": "D:/project/main.cpp",
                    "itemId": "1234567890"
                },
                {
                    "name": "main.cpp",
                    "description": "project/main.cpp",
                    "content": "#include<....",
                    "type": "1",
                    "uri": "D:/project/main.cpp",
                    "itemId": "1234567890"
                }
            ]
        }
    ]
    */

    QJsonDocument jsonDoc(contextsArray);
    return QString::fromUtf8(jsonDoc.toJson(QJsonDocument::Compact));
}

void ChatSession::loadContextsFromJsonString(const QString &contexJsonString)
{
    // 将 JSON 字符串转换为 QJsonDocument
    QJsonDocument jsonDoc = QJsonDocument::fromJson(contexJsonString.toUtf8());

    // 检查 JSON 文档是否有效
    if (!jsonDoc.isArray())
    {
        qWarning() << "Invalid JSON format: expected an array";
        return;
    }

    // 获取 JSON 数组
    QJsonArray contextsArray = jsonDoc.array();

    // 遍历 JSON 数组中的每个对象
    for (const QJsonValue &contextValue : contextsArray)
    {
        // 检查当前值是否为对象
        if (!contextValue.isObject())
        {
            qWarning() << "Invalid JSON format: expected an object";
            continue;
        }

        // 获取当前对象
        QJsonObject contextObj = contextValue.toObject();

        // 获取 id 和 contexts 数组
        QString id = contextObj.value("id").toString();
        QJsonArray contextItemArray = contextObj.value("contexts").toArray();

        // 创建一个 QList<ContextItem> 来存储当前 id 对应的 ContextItem 对象
        QList<ContextItem> contextItems;

        // 遍历 contexts 数组中的每个对象
        for (const QJsonValue &contextItemValue : contextItemArray)
        {
            // 检查当前值是否为对象
            if (!contextItemValue.isObject())
            {
                qWarning() << "Invalid JSON format: expected an object";
                continue;
            }

            // 获取当前对象
            QJsonObject contextItemObj = contextItemValue.toObject();

            // 创建一个新的 ContextItem 对象并从 JSON 对象中加载数据
            ContextItem contextItem;
            contextItem.fromJson(contextItemObj);

            // 将 ContextItem 对象添加到列表中
            contextItems.append(contextItem);
        }

        // 将 id 和对应的 ContextItem 列表添加到 mContexts 中
        mContexts.insert(id, contextItems);
    }
}

}
