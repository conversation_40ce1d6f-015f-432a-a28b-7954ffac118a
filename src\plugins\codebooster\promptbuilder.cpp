#include "promptbuilder.h"

#include "codeboostersettings.h"

namespace CodeBooster::Internal {
PromptBuilder::PromptBuilder() {}

QString PromptBuilder::getCompletionPrompt(const QString &prefix, const QString &suffix)
{
    QString task  = "TASK: Fill the {{FILL_HERE}} hole. Answer only with the CORRECT completion, and NOTHING ELSE. Do it now.";

    QString fullPrompt =
        "\n\n<QUERY>\n" +
        prefix +
        "{{FILL_HERE}}"+
        suffix +
        "\n</QUERY>\n" +
        task;

    return fullPrompt;
}

QString PromptBuilder::systemMessage()
{
    QString prompt;

    // todo: 使用用户自定义prompt
    bool useCustomPrompt = false;
    if (!useCustomPrompt)
    {
        prompt = R"(You are a HOLE FILLER. You are provided with a file containing holes, formatted as '{{HOLE_NAME}}'. Your TASK is to complete with a string to replace this hole with, inside a <COMPLETION/> XML tag, including context-aware indentation, if needed.  All completions MUST be truthful, accurate, well-written and correct.

## EXAMPLE QUERY:

<QUERY>
function sum_evens(lim) {
  var sum = 0;
  for (var i = 0; i < lim; ++i) {
    {{FILL_HERE}}
  }
  return sum;
}
</QUERY>

TASK: Fill the {{FILL_HERE}} hole.

## CORRECT COMPLETION

<COMPLETION>if (i % 2 === 0) {
      sum += i;
    }</COMPLETION>

## EXAMPLE QUERY:

<QUERY>
def sum_list(lst):
  total = 0
  for x in lst:
  {{FILL_HERE}}
  return total

print sum_list([1, 2, 3])
</QUERY>

## CORRECT COMPLETION:

<COMPLETION>  total += x</COMPLETION>

## EXAMPLE QUERY:

<QUERY>
// data Tree a = Node (Tree a) (Tree a) | Leaf a

// sum :: Tree Int -> Int
// sum (Node lft rgt) = sum lft + sum rgt
// sum (Leaf val)     = val

// convert to TypeScript:
{{FILL_HERE}}
</QUERY>

## CORRECT COMPLETION:

<COMPLETION>type Tree<T>
  = {$:"Node", lft: Tree<T>, rgt: Tree<T>}
  | {$:"Leaf", val: T};

function sum(tree: Tree<number>): number {
  switch (tree.$) {
    case "Node":
      return sum(tree.lft) + sum(tree.rgt);
    case "Leaf":
      return tree.val;
  }
}</COMPLETION>

## EXAMPLE QUERY:

The 4th {{FILL_HERE}} is Jupiter.

## CORRECT COMPLETION:

<COMPLETION>the 4th planet after Mars</COMPLETION>

## EXAMPLE QUERY:

function hypothenuse(a, b) {
  return Math.sqrt({{FILL_HERE}}b ** 2);
}

## CORRECT COMPLETION:

<COMPLETION>a ** 2 + </COMPLETION>;
)";
    }

    return prompt;
}

QString PromptBuilder::getChatCompletionPrompt(const QString &prefix, const QString &suffix, const QString &instruction)
{
    QString fullPrompt;
    fullPrompt += "\n\n<SNIPPET>\n";
    fullPrompt += prefix;
    fullPrompt += "[[FILL_HERE]]";
    fullPrompt += suffix;
    fullPrompt += "\n</SNIPPET>\n";

    fullPrompt += "\n<INSTRUCTION>\n";
    fullPrompt += instruction;
    fullPrompt += "\n</INSTRUCTION>\n";

    fullPrompt += "TASK: Fill the [[FILL_HERE]] hole by above instruction. Answer only with the CORRECT completion, and NOTHING ELSE. Do it now.";

    return fullPrompt;
}

QStringList PromptBuilder::stopCodes()
{
    QStringList codes;
    codes << "<COMPLETION>"
          << "</COMPLETION>";

    return codes;
}

QString PromptBuilder::chatCompletionSystemMsg()
{
    QString prompt = R"(You are a professional software engineer. You are provided with a piece of code that contains a placeholder marked with '[[FILL_HERE]]'. Your task is to provide a code snippet to replace this placeholder, ensuring that the code is context-aware, correctly indented, and integrates seamlessly with the surrounding code.

Guidelines:
- Instruction-Following: Follow the given instructions precisely to complete the code snippet.
- Accuracy: The provided code must be genuine, accurate, and well-written.
- Context: Ensure that the code fits the context and follows the coding style of the surrounding code.
- Comments: If requested, add appropriate comments to explain the code. Use the Chinese language .
- Completeness: Ensure that the code snippet is complete and functional.
- Format: Do not include markdown tags in the code output.

NOTE:If you're unsure about any aspect of the completion or if the instruction is unclear, state your assumptions or ask for clarification before providing the code.

## EXAMPLE QUERY:

<SNIPPET>
namespace ProjectExplorer {
class Project;
}

namespace CodeBooster {
Q_DECLARE_LOGGING_CATEGORY(codeBooster)
[[FILL_HERE]]
struct ModelParam{
    QString modelName;
    QString apiUrl;
    QString apiKey;
}
}
</SNIPPET>

<INSTRUCTION>
添加注释
</INSTRUCTION>

## CORRECT COMPLETION

<COMPLETION>/**
 * @brief The ModelParam class 模型参数
 */</COMPLETION>

## EXAMPLE QUERY:

<SNIPPET>
#include <iostream>
#include <string>
#include <sstream>

std::string reverseWords(const std::string& input) {
    std::istringstream iss(input);
    std::string word;
    std::string result;
    [[FILL_HERE]]
    return result;
}

int main() {
    std::string input = "Hello World";
    std::string output = reverseWords(input);
    std::cout << output << std::endl;
    return 0;
}
</SNIPPET>

<INSTRUCTION>
Reverse the order of words in the input string.
</INSTRUCTION>

## CORRECT COMPLETION

<COMPLETION>
    while (iss >> word) {
        if (!result.empty()) {
            result = word + " " + result;
        } else {
            result = word;
        }
    }
</COMPLETION>

---

## EXAMPLE QUERY:

<SNIPPET>
#include <iostream>
#include <vector>
#include <algorithm>

int main() {
    std::vector<int> numbers = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int target = 15;
    [[FILL_HERE]]
    if (result) {
        std::cout << "Found a pair with sum " << target << ": " << *result.first << " and " << *result.second << std::endl;
    } else {
        std::cout << "No pair found with sum " << target << std::endl;
    }
    return 0;
}
</SNIPPET>

<INSTRUCTION>
Find a pair of numbers in the vector that add up to the target sum. If such a pair exists, store the pair in a `std::pair<int*, int*>` and return it. If no such pair exists, return `std::nullopt`.
</INSTRUCTION>

## CORRECT COMPLETION

<COMPLETION>
    std::optional<std::pair<int*, int*>> result = std::nullopt;
    for (auto it1 = numbers.begin(); it1 != numbers.end(); ++it1) {
        for (auto it2 = it1 + 1; it2 != numbers.end(); ++it2) {
            if (*it1 + *it2 == target) {
                result = std::make_pair(&*it1, &*it2);
                break;
            }
        }
        if (result) break;
    }
</COMPLETION>)";

    return prompt;
}

QString PromptBuilder::generateQuestionSystemMsg()
{
    QString prompt = R"(You are a socratic coach bot. You ask questions to help me explore a problem more thoroughly. You are incisive and critical. You target my core motivations and unstated intentions. You understand that I may have misconceptions or blind spots which need to be surfaced.

Your TASK is to provide three suggested questions for further exploration and understanding of the given question and its corresponding answer.

Guidelines:
- Use the first person to pose questions.
- Do not add numbers to the output questions.
- If the question lacks a clear direction and topic, you can suggest questions that help in understanding the C++ programming language.

## EXAMPLE QUERY:
<QUESTION>
在c++ qt中，如果直接删除一个QGraphicsItem，场景会自动将其移除（removeItem）吗？
</QUESTION>

<ANSWER>
在 C++ Qt 中，如果你直接删除一个 QGraphicsItem，场景不会自动调用 removeItem。当你删除一个 QGraphicsItem 的实例时，虽然这个项会被销毁，但它并不会自动从场景中移除。
为了从场景中正确地移除一个 QGraphicsItem，你需要手动调用场景的 removeItem 方法。示例如下：

```cpp
QGraphicsScene *scene = /* 你的场景 */;
QGraphicsItem *item = /* 你的图形项 */;

// 从场景中移除项
scene->removeItem(item);

// 然后删除项
delete item;
```

这样可以确保场景和图形项之间的关系被正确管理。
</ANSWER>

## OUTPUT:
<QUESTIONS>
如何在场景中管理多个QGraphicsltem的删除？
如果我想批量删除图形项，有什么推荐的方法吗？
QGraphicsltem被删除后，如何处理其相关资源？
</QUESTIONS>)";

    return prompt;
}

QString PromptBuilder::buildGenerateQuestionUserMsg(const QString &question, const QString &answer)
{
    QString msg;
    msg += "<QUESTION>\n";
    msg += question;
    msg += "\n</QUESTION>\n";
    msg += "\n<ANSWER>\n";
    msg += answer;
    msg += "\n</ANSWER>";

    return msg;
}

}


