#include "codeboosterplugin.h"

#include <QAction>
#include <QToolButton>
#include <QMenu>
#include <QApplication>
#include <QTranslator>
#include <QTimeZone>

#include <coreplugin/actionmanager/actionmanager.h>
#include <coreplugin/editormanager/editormanager.h>
#include <coreplugin/icore.h>
#include <coreplugin/statusbarmanager.h>
#include <coreplugin/navigationwidget.h>
#include <cppeditor/cppcodemodelsettings.h>
#include <cppeditor/cppeditorconstants.h>
#include <languageclient/languageclientmanager.h>
#include <projectexplorer/projectpanelfactory.h>
#include <texteditor/textdocumentlayout.h>
#include <texteditor/texteditor.h>

#include "codeboosterclient.h"
#include "codeboosterconstants.h"
#include "codeboostericons.h"
#include "codeboosteroptionspage.h"
#include "codeboosterprojectpanel.h"
#include "codeboostersettings.h"
#include "codeboostersuggestion.h"
#include "codeboostertr.h"
#include "chatsidebar/chatviewfactory.h"
#include "chatsidebar/chatview.h"
#include "editorchatwindow.h"
#include "editorchatwindow.h"
#include "widgettheme.h"
#include "useroptions.h"
#include "chatsidebar/slashcommand/slashcommand.h"

using namespace Utils;
using namespace Core;
using namespace ProjectExplorer;

namespace CodeBooster {
namespace Internal {

class TextActionBuilder : public ActionBuilder
{
public:
    TextActionBuilder(QObject *contextActionParent, const Utils::Id actionId)
        : ActionBuilder(contextActionParent, actionId)
    {
        setContext(Context("Text Editor"));
    }
};


static CodeBoosterPlugin *m_instance = nullptr;

enum Direction { Previous, Next };
void cycleSuggestion(TextEditor::TextEditorWidget *editor, Direction direction)
{
    QTextBlock block = editor->textCursor().block();
    if (auto *suggestion = dynamic_cast<CodeBoosterSuggestion *>(
            TextEditor::TextDocumentLayout::suggestion(block))) {
        int index = suggestion->currentCompletion();
        if (direction == Previous)
            --index;
        else
            ++index;
        if (index < 0)
            index = suggestion->completions().count() - 1;
        else if (index >= suggestion->completions().count())
            index = 0;
        suggestion->reset();
        editor->insertSuggestion(std::make_unique<CodeBoosterSuggestion>(suggestion->completions(),
                                                                     editor->document(),
                                                                     index));
    }
}

CodeBoosterPlugin *CodeBoosterPlugin::instance()
{
    return m_instance;
}

void CodeBoosterPlugin::initialize()
{
    m_instance = this;

    // 初始化对话数据库
    ChatDatabase::instance();

    // 初始化样式
    Theme::instance();

    // 初始化配置文件
    UserOptions::instance();

    // 初始化自定义命令
    SlashCommandFactory::instance();

    // 翻译文本
    QTranslator *translator=new QTranslator(QApplication::instance());
    QTimeZone localPosition = QDateTime::currentDateTime().timeZone();
    if(QLocale::Country::China==localPosition.country()){
        if(translator->load("CodeBooster_zh_CN",QApplication::applicationDirPath()+"/../share/qtcreator/translations")){
            QApplication::installTranslator(translator);
        }
    }

    // 初始化客户端
    restartClient();

    connect(&CodeBoosterSettings::instance(),
            &CodeBoosterSettings::applied,
            this,
            &CodeBoosterPlugin::restartClient);


    /// 注册一些功能按钮
    // 问题：这个按钮在哪里触发？
    QAction *requestAction = new QAction(this);
    requestAction->setText(Tr::tr("请求CodeBooster自动补全"));
    requestAction->setToolTip(
        Tr::tr("在编辑器光标位置请求CodeBooster自动补全"));

    connect(requestAction, &QAction::triggered, this, [this] {
        if (auto editor = TextEditor::TextEditorWidget::currentTextEditorWidget()) {
            if (m_client && m_client->reachable())
                m_client->requestCompletions(editor);
        }
    });

    connect(EditorChat::instance(), &EditorChat::requestChatSuggestion, [this](TextEditor::TextEditorWidget *editor) {
            if (m_client && m_client->reachable())
                m_client->requestEditorChatCompletions(editor);

    });

    connect(EditorChat::instance(), &EditorChat::cancelRunningRequest, this, [this](TextEditor::TextEditorWidget *editor) {
        m_client->cancelChatRunningRequest(editor);
    });

   connect(this, &CodeBoosterPlugin::completeReplyStateChanged, [this](bool running) {
    if (mSpinner) {
        // 使用 QMetaObject::invokeMethod 确保在 mSpinner 所在的线程中调用 setVisible 方法
        // 这样可以避免直接跨线程调用对象方法导致的线程安全问题
        QMetaObject::invokeMethod(mSpinner, [this, running] {
            mSpinner->setVisible(running);
        }, Qt::QueuedConnection);
    }
});

    connect(this, &CodeBoosterPlugin::completeReplyStateChanged, EditorChat::instance(), &EditorChat::replyStateChanged);

    ActionManager::registerAction(requestAction, Constants::CODEBOOSTER_REQUEST_SUGGESTION);


    QAction *nextSuggestionAction = new QAction(this);
    nextSuggestionAction->setText(Tr::tr("Show next CodeBooster Suggestion"));
    nextSuggestionAction->setToolTip(Tr::tr(
        "Cycles through the received CodeBooster Suggestions showing the next available Suggestion."));

    connect(nextSuggestionAction, &QAction::triggered, this, [] {
        if (auto editor = TextEditor::TextEditorWidget::currentTextEditorWidget())
            cycleSuggestion(editor, Next);
    });

    ActionManager::registerAction(nextSuggestionAction, Constants::CODEBOOSTER_NEXT_SUGGESTION);

    QAction *previousSuggestionAction = new QAction(this);
    previousSuggestionAction->setText(Tr::tr("Show previos CodeBooster Suggestion"));
    previousSuggestionAction->setToolTip(Tr::tr("Cycles through the received CodeBooster Suggestions "
                                                "showing the previous available Suggestion."));

    connect(previousSuggestionAction, &QAction::triggered, this, [] {
        if (auto editor = TextEditor::TextEditorWidget::currentTextEditorWidget())
            cycleSuggestion(editor, Previous);
    });

    ActionManager::registerAction(previousSuggestionAction, Constants::CODEBOOSTER_PREVIOUS_SUGGESTION);

    QAction *disableAction = new QAction(this);
    disableAction->setText(Tr::tr("关闭 CodeBooster 自动补全"));
    disableAction->setToolTip(Tr::tr("关闭 CodeBooster 自动补全."));
    connect(disableAction, &QAction::triggered, this, [] {
        CodeBoosterSettings::instance().autoComplete.setValue(true);
        CodeBoosterSettings::instance().apply();
    });
    ActionManager::registerAction(disableAction, Constants::CODEBOOSTER_DISABLE);

    QAction *enableAction = new QAction(this);
    enableAction->setText(Tr::tr("开启 CodeBooster 自动补全"));
    enableAction->setToolTip(Tr::tr("开启 CodeBooster 自动补全."));
    connect(enableAction, &QAction::triggered, this, [] {
        CodeBoosterSettings::instance().autoComplete.setValue(false);
        CodeBoosterSettings::instance().apply();
    });
    ActionManager::registerAction(enableAction, Constants::CODEBOOSTER_ENABLE);

    QAction *toggleAction = new QAction(this);
    toggleAction->setText(Tr::tr("开启CodeBooster自动补全"));
    toggleAction->setCheckable(true);
    toggleAction->setChecked(CodeBoosterSettings::instance().autoComplete.value());
    toggleAction->setIcon(CODEBOOSTER_ICON.icon());
    connect(toggleAction, &QAction::toggled, this, [](bool checked) {
        CodeBoosterSettings::instance().autoComplete.setValue(checked);
        CodeBoosterSettings::instance().apply();
    });

    ActionManager::registerAction(toggleAction, Constants::CODEBOOSTER_TOGGLE);

    {
        auto updateActions = [toggleAction, requestAction, this] {
            const bool enabled = CodeBoosterSettings::instance().autoComplete.value();
            toggleAction->setToolTip(enabled ? Tr::tr("关闭CodeBooster自动补全") : Tr::tr("开启CodeBooster自动补全"));
            toggleAction->setChecked(enabled);
            requestAction->setEnabled(enabled);

            if (!enabled)
            {
                if (mSpinner)
                    mSpinner->setVisible(false);
            }
        };

        connect(&CodeBoosterSettings::instance().autoComplete,
                &BoolAspect::changed,
                this,
                updateActions);

        updateActions();
    }

    // 代码补全开关
    auto toggleButton = new QToolButton;
    toggleButton->setDefaultAction(toggleAction);
    StatusBarManager::addStatusBarWidget(toggleButton, StatusBarManager::RightCorner);

    mSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Small, toggleButton);
    mSpinner->setVisible(false);


    {
        // auto mtools = ActionManager::actionContainer(Core::Constants::M_TOOLS);
        // ActionContainer *codeboosterTool = ActionManager::createMenu(Constants::M_CODEBOOSTER_TOOL);
        // codeboosterTool->setOnAllDisabledBehavior(ActionContainer::Hide);
        // codeboosterTool->menu()->setTitle(Tr::tr("CodeBooster Tool"));
        // mtools->addMenu(codeboosterTool);

        // QAction *generateAction = new QAction(Tr::tr("AI 生成"), this);
        // generateAction->setMenuRole(QAction::ApplicationSpecificRole);
        // Command *generateCommand = ActionManager::registerAction(generateAction, "CodeBooster.Generate");
        // //connect(generateAction, &QAction::triggered, this, &UpdateInfoPlugin::startCheckForUpdates);
        // codeboosterTool->addAction(generateCommand);

        // TextActionBuilder(this, "TextEditor.CodeBoosterGenerate")
        //     .setText(Tr::tr("使用CodeBooster生成"))
        //     .setDefaultKeySequence(QKeySequence(Tr::tr("Alt+K")));

        const char * const menuGroupId = "CodeBoosterCppGroup";
        ActionContainer * const mtoolscpp
            = ActionManager::actionContainer(CppEditor::Constants::M_CONTEXT);
        if (mtoolscpp) {
            mtoolscpp->insertGroup(CppEditor::Constants::G_GLOBAL, menuGroupId);
            mtoolscpp->addSeparator(menuGroupId);
        }
        Core::ActionContainer * const mcontext = Core::ActionManager::actionContainer(
            CppEditor::Constants::M_CONTEXT);
        if (mcontext) {
            mcontext->insertGroup(CppEditor::Constants::G_GLOBAL, menuGroupId);
            mcontext->addSeparator(menuGroupId);
        }

        QAction *generateAction = new QAction(Tr::tr("AI 生成"), this);
        Command *generateCommand = ActionManager::registerAction(generateAction, "CppEditor.Generate");
        generateCommand->setDefaultKeySequence(QKeySequence(Tr::tr("Alt+K")));
        connect(generateAction, &QAction::triggered, this, [] {
            EditorChat::instance()->openChatWindow();
        });

        if (mtoolscpp)
            mtoolscpp->addAction(generateCommand, menuGroupId);
    }


    // 注册工程设置面板 
    setupCodeBoosterProjectPanel();

    // 注册侧边栏
    // setupChatViewWidgetFactory();
    ChatViewFactory::instance();

    // 注册编译问题提问按钮
    connect(&mAskCompileErrorHandler, &AskCodeBoosterTaskHandler::askCompileError, this, &CodeBoosterPlugin::onHandleAskCodeBossterTask);
}

void CodeBoosterPlugin::extensionsInitialized()
{
    // 初始化偏好设置界面
    (void)CodeBoosterOptionsPage::instance();
}

void CodeBoosterPlugin::restartClient()
{
    LanguageClient::LanguageClientManager::shutdownClient(m_client);

    m_client = new CodeBoosterClient();
    connect(m_client, &CodeBoosterClient::documentSelectionChanged, this, &CodeBoosterPlugin::documentSelectionChanged);
}

ExtensionSystem::IPlugin::ShutdownFlag CodeBoosterPlugin::aboutToShutdown()
{
    if (!m_client)
        return SynchronousShutdown;
    connect(m_client, &QObject::destroyed, this, &IPlugin::asynchronousShutdownFinished);
    return AsynchronousShutdown;
}

// void CodeBoosterPlugin::completeReplyStateChanged(bool running)
// {
//     qDebug() << Q_FUNC_INFO << QThread::currentThreadId();
//     EditorChat::instance()->replyStateChanged(running);
// }

void CodeBoosterPlugin::onHandleAskCodeBossterTask(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts)
{
    // 激活对话侧边栏
    // TODO: ChatViewManager多余，应该使用ChatViewFactory管理
    ChatView *view = ChatViewManager::instance().getOneView();
    if (view)
    {
        // 对话控件没有悬浮时激活侧边栏
        if (!view->isFloating())
        {
            // NOTE: 可能在左侧，who knows?
            Core::NavigationWidget::activateSubWidget(Constants::CODEBOOSTER_CHAT_VIEW_ID, Core::Side::Right);
        }
        else
        {
            view->activateWindow();
        }
    }
    else
    {
        Core::NavigationWidget::activateSubWidget(Constants::CODEBOOSTER_CHAT_VIEW_ID, Core::Side::Right);
        view = ChatViewManager::instance().getOneView();
    }

    if (view)
    {
        if (ChatViewFactory::instance().isParallelChat())
        {
            for (auto cv : ChatViewManager::instance().allViews())
            {
                cv->sendUserMessageNoHistory(sysMsg, userMsg, contexts);
            }
        }
        else
        {
            view->sendUserMessageNoHistory(sysMsg, userMsg, contexts);
        }
    }
}

} // namespace Internal
} // namespace CodeBooster
