#pragma once

#include <utils/icon.h>

namespace CodeBooster {
static const Utils::Icon CODEBOOSTER_ICON({{":/codebooster/images/codebooster.png",
                                        Utils::Theme::IconsBaseColor}});

static const Utils::Icon CHAT_ICON({{":/codebooster/images/chat.png",
                                     Utils::Theme::IconsBaseColor}});

static const Utils::Icon HISTORY_ICON({{":/codebooster/images/history.png",
                                     Utils::Theme::IconsBaseColor}});

static const Utils::Icon BACK_ICON({{":/codebooster/images/back.png",
                                        Utils::Theme::IconsBaseColor}});

static const Utils::Icon DELETE_ICON({{":/codebooster/images/delete.png",
                                     Utils::Theme::IconsBaseColor}});

static const Utils::Icon DELETE_ICON_INFO({{":/codebooster/images/delete.png",
                                       Utils::Theme::IconsInfoColor}});

static const Utils::Icon EDIT_ICON({{":/codebooster/images/edit.png",
                                       Utils::Theme::IconsBaseColor}});

static const Utils::Icon EDIT_ICON_INFO({{":/codebooster/images/edit.png",
                                     Utils::Theme::IconsInfoColor}});

static const Utils::Icon USER_ICON_INFO({{":/codebooster/images/user.png",
                                          Utils::Theme::IconsRunColor}});

static const Utils::Icon ROBOT_ICON({{":/codebooster/images/robot.png",
                                           Utils::Theme::IconsBaseColor}});

static const Utils::Icon ROBOT_ICON_INFO({{":/codebooster/images/robot.png",
                                          Utils::Theme::IconsInfoColor}});

static const Utils::Icon COPY_ICON({{":/codebooster/images/copy.png",
                                           Utils::Theme::IconsInfoColor}});

static const Utils::Icon INSERT_ICON({{":/codebooster/images/insert.png",
                                          Utils::Theme::IconsInfoColor}});

static const Utils::Icon CODEFILE_ICON({{":/codebooster/images/codefile.png",
                                       Utils::Theme::IconsBaseColor}});

static const Utils::Icon EXPAND_ICON({{":/codebooster/images/expand.png",
                                         Utils::Theme::IconsBaseColor}});

static const Utils::Icon COLLAPSE_ICON({{":/codebooster/images/collapse.png",
                                         Utils::Theme::IconsBaseColor}});

static const Utils::Icon NEW_ICON({{":/codebooster/images/new.png",
                                         Utils::Theme::IconsBaseColor}});

static const Utils::Icon ENLARGE_ICON({{":/codebooster/images/enlarge.png",
                                    Utils::Theme::IconsBaseColor}});

static const Utils::Icon DOCK_ICON({{":/codebooster/images/app-on-top.png",
                                        Utils::Theme::IconsBaseColor}});

static const Utils::Icon PARALLEL_ICON({{":/codebooster/images/parallel.png",
                                     Utils::Theme::IconsBaseColor}});

static const Utils::Icon HELPCENTER_ICON({{":/codebooster/images/help-center.png",
                                         Utils::Theme::IconsBaseColor}});

static const Utils::Icon UPGRADE_ICON({{":/codebooster/images/upgrade.png",
                                           Utils::Theme::IconsBaseColor}});

static const Utils::Icon UPDATEINFO_ICON({{":/codebooster/images/update-info.png",
                                        Utils::Theme::IconsBaseColor}});


static const QIcon ICON_STOP(){static QIcon icon(":/codebooster/images/stop.png");return icon;}

}
