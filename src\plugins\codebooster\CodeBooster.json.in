{
    "Name" : "CodeBooster",
    "Version" : "0.5.2",
    "CompatVersion" : "${IDE_VERSION_COMPAT}",
    "DisabledByDefault" : false,
    "Vendor" : "WangMao",
    "Copyright" : "(C) 2024 WangMao",
    "License": [
        "Proprietary License",
        "",
        "1. License Grant",
        "   Licensor hereby grants to Licensee a non-exclusive, non-transferable, revocable license to use the software (\"Software\") solely for internal business purposes.",
        "",
        "2. Restrictions",
        "   Licensee shall not:",
        "   a. Copy, modify, or distribute the Software;",
        "   b. <PERSON>erse engineer, decompile, or disassemble the Software;",
        "   c. Transfer, sublicense, or assign the Software to any third party.",
        "",
        "3. Ownership",
        "   Licensor retains all rights, title, and interest in and to the Software, including all intellectual property rights.",
        "",
        "4. Termination",
        "   This Agreement is effective until terminated. Licensor may terminate this Agreement if Licensee breaches any of its terms.",
        "",
        "5. Disclaimer of Warranty",
        "   The Software is provided \"AS IS\" without warranty of any kind. Licensor disclaims all warranties, express or implied, including, but not limited to, the implied warranties of merchantability and fitness for a particular purpose.",
        "",
        "6. Limitation of Liability",
        "   In no event shall Licensor be liable for any damages arising out of the use or inability to use the Software."
      ],
    "Description" : "CodeBooster",
    "Url" : "https://www.qt.io",
    ${IDE_PLUGIN_DEPENDENCIES}
}
