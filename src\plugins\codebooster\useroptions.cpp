#include "useroptions.h"

#include "codeboosterutils.h"

namespace CodeBooster {

UserOptions::UserOptions() :
    sideBarChatModelTitle(),
    editorChatModelTitle(),
    showEditorSelection(true),
    mSetting(QSettings(Internal::dataFolderPath() + "/remember.ini", QSettings::IniFormat))

{
    // 初始化配置
    init();
}

UserOptions::~UserOptions()
{
    save();
}

UserOptions &UserOptions::instance()
{
    static UserOptions uo;
    return uo;
}

void UserOptions::save()
{
    // 开始分组
    {
        mSetting.beginGroup("SideBarChat");
        mSetting.setValue("sideBarChatModelTitle", sideBarChatModelTitle);
        mSetting.setValue("showEditorSelection", showEditorSelection);
        mSetting.endGroup();
    }

    {
        mSetting.beginGroup("InEditorChat");
        mSetting.setValue("editorChatModelTitle", editorChatModelTitle);
        mSetting.endGroup();
    }

    mSetting.sync();
}

void UserOptions::init()
{
    {
        mSetting.beginGroup("SideBarChat");
        sideBarChatModelTitle = mSetting.value("sideBarChatModelTitle").toString();
        showEditorSelection = mSetting.value("showEditorSelection", true).toBool();
        mSetting.endGroup();
    }

    {
        mSetting.beginGroup("InEditorChat");
        editorChatModelTitle = mSetting.value("editorChatModelTitle").toString();
        mSetting.endGroup();
    }
}

int UserOptions::getLastCompletionReplySize()
{
    QMutexLocker locker(&mMutex);
    return LastCompletionReplySize;
}

void UserOptions::setLastCompletionReplySize(int size)
{
    QMutexLocker locker(&mMutex);
    LastCompletionReplySize = size;
}


} // namespace CodeBooster
