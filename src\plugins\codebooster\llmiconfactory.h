#ifndef LLMICONFACTORY_H
#define LLMICONFACTORY_H

#include <QIcon>
#include "utils/utilsicons.h"

namespace CodeBooster {

static QIcon llmIcon(const QString &modelName)
{
    // Map of model prefixes to their corresponding icons
    QMap<QString, QString> iconMap = {
        {"claude",    ":/codebooster/images/llm/claude.png"},
        {"deepseek",  ":/codebooster/images/llm/deepseek.png"},
        {"gemini",    ":/codebooster/images/llm/gemini.png"},
        {"gemma",     ":/codebooster/images/llm/gemma.png"},
        {"glm",       ":/codebooster/images/llm/glm.png"},
        {"gpt",       ":/codebooster/images/llm/gpt.png"},
        {"llama",     ":/codebooster/images/llm/llama.png"},
        {"mistral",   ":/codebooster/images/llm/mistral.png"},
        {"qwen",      ":/codebooster/images/llm/qwen.png"}
    };

    // Convert modelName to lowercase for case-insensitive comparison
    QString lowerModelName = modelName.toLower();

    // Iterate through the map to find a matching prefix
    for (auto it = iconMap.constBegin(); it != iconMap.constEnd(); ++it) {
        if (lowerModelName.contains(it.key())) {
            return QIcon(it.value());
        }
    }

    Utils::Icon ROBOT_ICON({{":/codebooster/images/robot.png",
                                          Utils::Theme::IconsBaseColor}});

    // Return a default icon if no match is found
    return ROBOT_ICON.icon();
}

}

#endif // LLMICONFACTORY_H
