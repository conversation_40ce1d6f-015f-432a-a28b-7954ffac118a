#include "contextitem.h"

#include <QIcon>
#include <QUuid>
#include <QFileIconProvider>
#include <QFileInfoList>

#include "codeboostericons.h"
#include "codeboosterutils.h"

namespace CodeBooster {

ContextItem::ContextItem() : itemId(QUuid::createUuid().toString())
{

}

ContextItem ContextItem::buildFileContextFromFilePath(const Utils::FilePath &filePath, bool &success)
{
    QString path = filePath.absoluteFilePath().path();

    success = true;
    QString content = Internal::readTextFile(path, success);
    if (success)
    {
        ContextItem item;
        item.name = filePath.fileName();
        item.description = path;
        item.content = content;
        item.type = ContextItem::File;
        item.uri = path;

        return item;
    }

    return ContextItem();
}

QIcon ContextItem::icon() const
{
    if ( (File == type) || (Folder == type) )
    {
        // 创建 QFileIconProvider 实例
        QFileIconProvider iconProvider;

        // 获取文件信息
        QFileInfo fileInfo(uri);

        // 获取图标
        QIcon icon = iconProvider.icon(fileInfo);

        return icon;
    }
    else
    {
        return CODEFILE_ICON.icon();
    }

    return QIcon();
}

QString ContextItem::typeName() const
{
    static QMap<ContextType, QString> nameMapping {
        {File,   "文件" },
        {Folder, "文件夹"},
        {Custom, "自定义"}
    };

    return nameMapping.value(type);
}

QString ContextItem::contextText() const
{
    QString text;

    if (File == type)
    {
        text += "<FILE>\n";
        text += content;
        text += "\n</FILE>";

    }
    else if (Folder == type)
    {
        text += "<FOLDER>\n";
        text += QString("Folder Name: %1\n").arg(name);
        text += QString("Folder Structure:\n%1\n").arg(generateTreeText(uri));
        text += QString("Folder Content:\n");
        text += content;
        text += "\n</FOLDER>";
    }

    return text;
}

/**
 * @brief ContextItem::tagText 在编辑器中显示的标签
 * @return
 */
QString ContextItem::tagText() const
{
    QString tag = QString(" @%1:%2 ").arg(typeName()).arg(name);
    return tag;
}

/**
 * @brief ContextItem::toJson
 * @return
 */
QJsonObject ContextItem::toJson() const
{
    /*
    {
        "name": "main.cpp",
        "description": "project/main.cpp",
        "content": "#include<....",
        "type": "1",
        "uri": "D:/project/main.cpp",
        "itemId": "1234567890"
    }
    */
    QJsonObject obj;
    obj.insert("name", name);
    obj.insert("description", description);
    obj.insert("content", content);
    obj.insert("type", type);
    obj.insert("uri", uri);
    obj.insert("itemId", itemId);

    return obj;
}

void ContextItem::fromJson(const QJsonObject &json)
{
    name = json.value("name").toString();
    description = json.value("description").toString();
    content = json.value("content").toString();
    type = (ContextType)json.value("type").toInt();
    uri = json.value("uri").toString();
    itemId = json.value("itemId").toString();
}

QString ContextItem::generateTreeText(const QString &path, int indentLevel) const
{
    QString treeText;
    QDir dir(path);
    QFileInfoList entryList = dir.entryInfoList(QDir::Dirs | QDir::Files | QDir::NoDotAndDotDot);

    // 添加当前文件夹名称到树形结构的顶部
    if (indentLevel == 0) {
        QFileInfo folderInfo(path);
        treeText += "+" + folderInfo.fileName() + "\n";
        indentLevel++;
    }

    for (const QFileInfo &entry : entryList) {
        // 添加缩进
        for (int i = 0; i < indentLevel; ++i) {
            treeText += "    ";
        }

        // 添加条目名称
        treeText += entry.isDir() ? "+" : "-";
        treeText += entry.fileName() + "\n";

        // 如果是文件夹，递归处理子文件夹
        if (entry.isDir()) {
            treeText += generateTreeText(entry.absoluteFilePath(), indentLevel + 1);
        }
    }

    return treeText;
}

} // namespace CodeBooster
