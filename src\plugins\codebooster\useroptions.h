#ifndef USEROPTIONS_H
#define USEROPTIONS_H

#define USER_OP UserOptions::instance()

#include <QSettings>
#include <QMutex>


namespace CodeBooster {

class UserOptions
{
public:
    UserOptions();
    ~UserOptions();

public:
    static UserOptions &instance();

public:
    QString sideBarChatModelTitle; ///< 对话侧边栏模型标题
    bool showEditorSelection;     ///< 显示编辑器选中文本
    QString editorChatModelTitle;  ///< 编辑器内对话控件模型标题

private:
    /**
     * @brief save
     */
    void save();
    void init();

private:
    QSettings mSetting;

    // 当成临时存储使用
public:
    int getLastCompletionReplySize();
    void setLastCompletionReplySize(int size);

private:
    ///< 上次自动补全回答的长度
    int LastCompletionReplySize = 0;
    QMutex mMutex;
};

} // namespace CodeBooster

#endif // USEROPTIONS_H
