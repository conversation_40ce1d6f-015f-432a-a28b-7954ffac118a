#ifndef PROMPTBUILDER_H
#define PROMPTBUILDER_H

#include <QString>

namespace CodeBooster::Internal {
class PromptBuilder
{
public:
    PromptBuilder();

    static QString getCompletionPrompt(const QString &prefix, const QString &suffix);
    static QString systemMessage();

    static QString getChatCompletionPrompt(const QString &prefix, const QString &suffix, const QString &instruction);
    static QString chatCompletionSystemMsg();

    static QStringList stopCodes();


    static QString generateQuestionSystemMsg();
    static QString buildGenerateQuestionUserMsg(const QString &question, const QString &answer);
};
}

#endif // PROMPTBUILDER_H
