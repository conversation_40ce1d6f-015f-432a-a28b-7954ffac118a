#pragma once

#include <coreplugin/inavigationwidgetfactory.h>
#include <utils/filepath.h>

#include <QPointer>
#include <QWidget>
#include <QPushButton>

#include "contextitem.h"

namespace Utils {
class ElidingLabel;
class NavigationTreeView;
} // Utils


namespace CodeBooster::Internal {

class ChatView;

class ChatViewGroup : public QObject
{
    Q_OBJECT
public:
    explicit ChatViewGroup();
    ~ChatViewGroup();

    QList<QToolButton *> toolButtons();
    QWidget *viewContainer() const;
    ChatView *chatView() const;

    void setParallelButtonCheckState(bool checked);

public:
    QPointer<QWidget> container;
    QPointer<QDialog> seperateDialog;

    QPointer<ChatView> view;
    QPointer<QToolButton> parallelChatButton;
    QPointer<QAction> parallelChatAction;

    QPointer<QToolButton> seperateWindowButton;
    QPointer<QAction> seperateWindowAction;

    QPushButton* undockViewBtn;

signals:
    void needDestroy();
    void parallelChatActionTriggered(bool checked);
    void toSendUserMessage(const QString &message, const QList<ContextItem> &contexts);
};

class ChatViewFactory : public Core::INavigationWidgetFactory
{
    Q_OBJECT
public:
    ChatViewFactory();
    static ChatViewFactory &instance();
    ~ChatViewFactory() override;

    bool isParallelChat() const {return parallelChat; }

private:
    Core::NavigationView createWidget() override;

private:
    QList<QPointer<ChatViewGroup >> mViewGroups;
    bool parallelChat;
};

//void setupChatViewWidgetFactory();

}
