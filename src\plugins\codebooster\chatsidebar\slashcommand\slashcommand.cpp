#include "slashcommand.h"

#include "codeboosterutils.h"

namespace CodeBooster::Internal{


SlashCommandFactory::SlashCommandFactory()
{
    reloadCommands();
}

SlashCommandFactory::~SlashCommandFactory()
{

}

SlashCommandFactory &SlashCommandFactory::instance()
{
    static SlashCommandFactory inst;
    return inst;
}

int SlashCommandFactory::reloadCommands()
{
    QString folderPath = dataFolderPath();
    QString commandsFolderPath = folderPath + "/commands";

    if (!QDir(commandsFolderPath).exists())
    {
        QDir().mkpath(commandsFolderPath);
    }

    mAllCmds.clear();

    QDir commandsDir(commandsFolderPath);
    QFileInfoList entries = commandsDir.entryInfoList(QStringList("*.prompt"), QDir::Files);
    for (const QFileInfo &entry : entries)
    {
        QString name = entry.baseName();
        bool ok = true;
        SlashCommand cmd =SlashCommand(entry.absoluteFilePath(), ok);
        if (!ok)
        {
            continue;
        }

        mAllCmds.insert(name, cmd);
    }

    return mAllCmds.size();
}

SlashCommand SlashCommandFactory::getCommand(const QString &name)
{
    return mAllCmds.value(name);
}

QStringList SlashCommandFactory::commandNames() const
{
    return mAllCmds.keys();
}


// -------------------------------------------------------------------------
//
// -------------------------------------------------------------------------
SlashCommand::SlashCommand(const QString &filePath, bool &ok)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        // 处理文件打开失败的情况
        ok = false;
        return;
    }

    QTextStream in(&file);
    QString content = in.readAll();
    file.close();

    // 获取文件名
    mName = QFileInfo(filePath).baseName();

    // 去除注释
    content = removeComments(content);
    // 调试
    saveToTxtFile(content);
    // end

    // 解析文件内容
    parseContent(content);

    // 如果解析成功，设置标志位
    if (mUserMsg.isEmpty())
    {
        ok = false;
        return;
    }

    ok = true;
}

QString SlashCommand::removeComments(const QString &content)
{
    QString cleanedContent;
    QTextStream stream(content.toUtf8());

    while (!stream.atEnd())
    {
        QString line = stream.readLine();
        if (!line.trimmed().startsWith("#"))
        {
            cleanedContent.append(line).append("\n");
        }
    }

    // 移除最后一个多余的换行符
    if (!cleanedContent.isEmpty())
    {
        cleanedContent.chop(1);
    }

    return cleanedContent;
}

void SlashCommand::parseContent(const QString &content)
{
    // 使用正则表达式分割内容
    QRegularExpression separator("---");
    QRegularExpressionMatch match = separator.match(content);

    if (match.hasMatch())
    {
        // 分割为两部分
        QString modelParamsSection = content.left(match.capturedStart()).trimmed();
        QString messagesSection = content.mid(match.capturedEnd()).trimmed();

        // 解析模型参数
        parseModelParams(modelParamsSection);

        // 解析消息
        parseMessages(messagesSection);
    }
    else
    {
        // 如果没有 --- 分隔符，则整个内容都是消息部分
        parseMessages(content.trimmed());
    }
}

void SlashCommand::parseModelParams(const QString &modelParamsSection)
{
    QRegularExpression paramRegex("(\\w+)\\s*:\\s*([\\d.]+|\\d)");
    QRegularExpressionMatchIterator it = paramRegex.globalMatch(modelParamsSection);

    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        QString key = match.captured(1);
        QVariant value = match.captured(2);
        mOverrideModelParams.insert(key, value);
    }
}

void SlashCommand::parseMessages(QString messagesSection)
{
    //TODO: 标签解析顺序有问题
    // 找到所有标签的位置
    QRegularExpression descriptionRegex("<description>(.*)</description>", QRegularExpression::DotMatchesEverythingOption);
    QRegularExpression systemMsgRegex("<system>(.*)</system>", QRegularExpression::DotMatchesEverythingOption);

    QRegularExpressionMatch descriptionMatch = descriptionRegex.match(messagesSection);


    // 解析 description 标签
    if (descriptionMatch.hasMatch())
    {
        mDescription = descriptionMatch.captured(1).trimmed();
        qDebug() << "descriptionMatch.capturedStart(" << descriptionMatch.capturedStart()
                 << "descriptionMatch.capturedLength()" << descriptionMatch.capturedLength();
        messagesSection.remove(descriptionMatch.capturedStart(), descriptionMatch.capturedLength());
    }

    // 解析 system 标签
    QRegularExpressionMatch systemMatch = systemMsgRegex.match(messagesSection);
    if (systemMatch.hasMatch())
    {
        mSystemMsg = systemMatch.captured(1).trimmed();
        qDebug() << "systemMatch.capturedStart(" << systemMatch.capturedStart()
                 << "systemMatch.capturedLength()" << systemMatch.capturedLength();
        messagesSection.remove(systemMatch.capturedStart(), systemMatch.capturedLength());
    }

    // 剩余内容为用户消息
    mUserMsg = messagesSection.trimmed();
}

}
