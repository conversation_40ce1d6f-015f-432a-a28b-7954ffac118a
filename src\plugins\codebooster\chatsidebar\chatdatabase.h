#ifndef CHATDATABASE_H
#define CHATDATABASE_H

#include <QString>
#include <QJsonArray>
#include <QJsonObject>
#include <QSqlDatabase>

#include "contextitem.h"

#define CHAT_DB ChatDatabase::instance()

namespace CodeBooster::Internal{

class ChatSession;

struct ChatSessionBrief
{
    QString uuid;
    QString title;
    qint64 modifiedTime;
    int messageCount = 0;
};

class ChatDatabase
{
public:
    ChatDatabase();
    ~ChatDatabase();

public:
    static ChatDatabase &instance();
private:
    static QString sessionTableSql;         ///< 侧边栏对话记录表
    static QString editorChatInstTableSql; ///< 编辑器内对话指令表

public:
    bool saveChatSession(const ChatSession &session, QString &err);
    bool deleteChatSession(const QString &uuid, QString &err) const;
    bool loadSessionByUuid(const QString &uuid, ChatSession &session);
    bool deleteAllSessions();
    QList<ChatSessionBrief> loadAllSessions() const;
    bool loadSessionsByIndexRange(int start, int end, QList<ChatSessionBrief> &sessions);
    int sessionCount();

    int searchResultCount(const QString &keyword);
    bool searchMessage(const QString &keyword, QStringList &uuids);
    bool searchMessage(const QString &keyword, int start, int end, QList<ChatSessionBrief> &sessions);

public:
    bool saveInstruction(const QString &inst);
    QStringList latestUniqueInstructions(int count);

public:
    QString lastError() const;

private:
    QSqlDatabase mDb;
    QString mLastError;
};

/********************************************************************
 ChatSession
 聊天对话上下文
*********************************************************************/
class ChatSession
{
    friend class ChatDatabase; // 声明 ChatDatabase 为友元类

public:
    enum Role
    {
        User = 1,
        Assistant = 2
    };

public:
    ChatSession(const QString &sysMsg = QString());

    QString uuid() const {return mUuid;}
    QString title() const {return mTitle;}

    void setChatTitle(const QString &title);

    QJsonArray chatStorage() const;
    QString messageSource(int index) const;

    QString allContextToJsonString() const;
    void loadContextsFromJsonString(const QString &contexJsonString);

    void appendAssistantMessage(const QString &msg, const QString &model);

    QJsonArray getChatMessage(int maxMessageCount = 1, bool useSysMsg = true);
    QJsonArray getChatMessage(const QString &msg, const QList<ContextItem> &contexts, int maxMessageCount = 1);
    QJsonArray getChatMessage(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts);

    QList<ContextItem> getMessageContextsById(QString id);

    QString getLastAssistantMessageContent() const;
    QString getLastUserMessageContent() const;

    void appendUserMessage(const QString &msg, const QList<ContextItem> &contexts = QList<ContextItem>());

    QString readableTime() const;

    ChatSessionBrief toBrief() const;

    static QString readableTime(int timeStamp);

private:
    QJsonObject systemMsg() const;
    QJsonObject makeContentObject(Role role, const QString &msg) const;
    QJsonObject getMessageObjByFromIndex(int index);

private:
    QString mUuid;
    QString mTitle;

    QJsonArray mChatStorage;
    QStringList mMessageSourceNames; ///< 记录消息的来源，主要是为了记录每条回答的模型的名称
    QMap<QString, QList<ContextItem>> mContexts;

    qint64 mModifiedTime;

    QJsonObject mSysMsg;
};

}
#endif // CHATDATABASE_H
