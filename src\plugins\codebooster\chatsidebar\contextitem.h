#ifndef CONTEXTITEM_H
#define CONTEXTITEM_H

#include <QString>
#include <QJsonObject>

#include "utils/filepath.h"

namespace CodeBooster {

class ContextItem
{
public:
    ContextItem();

    static ContextItem buildFileContextFromFilePath(const Utils::FilePath &filePath, bool &success);

    enum ContextType
    {
        File = 1,
        Folder = 2,
        Custom = 255
    };

    QString name;
    QString description;
    QString content;
    ContextType    type;
    QString uri;
    QString itemId;


    QJsonObject toJson() const;
    void fromJson(const QJsonObject &json);

    QIcon icon() const;
    QString typeName() const;
    QString contextText() const;

    QString tagText() const;

private:
    QString generateTreeText(const QString &path, int indentLevel = 0) const;
};

} // namespace CodeBooster

#endif // CONTEXTITEM_H
