#ifndef EDITORCHATWINDOW_H
#define EDITORCHATWINDOW_H

#include <QWidget>
#include <QAction>
#include <QToolBar>
#include <QJsonArray>

#include <texteditor/texteditor.h>
#include <languageserverprotocol/lsptypes.h>
#include <solutions/spinner/spinner.h>

#include "codebooster/codeboostersettings.h"

namespace Ui {
class EditorChatWindow;
}

namespace CodeBooster::Internal{

class EditorChatWindow : public QWidget
{
    Q_OBJECT
public:
    explicit EditorChatWindow(QStringList history, QWidget *parent = nullptr);
    ~EditorChatWindow();

    void activateInput();
    TextEditor::TextEditorWidget *textEditor() const;

    void updatePosition();
    void replyStateChanged(bool running);

    void setCurrentModel(const QString &modelName);

    void scrollEditorToShowWindow();

protected:
    bool event(QEvent *e) override;
    void keyPressEvent(QKeyEvent *e) override;

private slots:
    void inputReturnPressed();
    void instTextChanged(QString text);
    void instSeleted(int index);
    void closeWindow();
    void requestTimeout();

private:
    void focusTextEditor();
    ModelParam currentModelParam() const;
    void hilightEditorLine(bool hl);
    void setSuggestionBtnState(bool visible);
    void setAcceptSuggestion(bool accept);
    bool requestIsRunning() const;
    void cancelRunningRequest();

signals:
    void sendInstructionText(const QString &text, CodeBooster::ModelParam param);
    void modelChanged(const QString &modelName);
    void cancelRequest(TextEditor::TextEditorWidget *editor);

private:
    Ui::EditorChatWindow *ui;

    TextEditor::TextEditorWidget *mTextEditor;

    QAction *mActionClose;
    QToolBar *mToolBar;

    bool mFirstShow;
    bool mFirstActivate;
    int  mPosition;

    QTextCursor mCompletionPosCursor;
    QTextCursor mEditorCursor; ///< 进入指令对话框前的指针
    bool mEditorHilightLine;

    int mLastVisibleSliderPos;

    SpinnerSolution::Spinner *mSpinner{nullptr};

    QTimer *mRequestTimer;
};

class EditorChat : public QObject
{
    Q_OBJECT
public:
    EditorChat();

    static EditorChat *instance();

    static void adjustCursor(QTextCursor &textCursor);

    void openChatWindow();
    EditorChatWindow *currentChatWindow() const;

    QString instructionText() const;
    CodeBooster::ModelParam instructionParam() const;
    LanguageServerProtocol::Position lspPosition() const;
    int editorPosition() const;

    void replyStateChanged(bool running);
    QStringList instructionHistory() const;

signals:
    void requestChatSuggestion(TextEditor::TextEditorWidget *editor);
    void cancelRunningRequest(TextEditor::TextEditorWidget *editor);

private:
    void setInstructionInfo(const QString &instText, CodeBooster::ModelParam modelParam);
    void windowClosed(QObject *obj);
    void remeberInstructionHistory(const QString &instText);

private:
    EditorChatWindow *mCurrentWindow;

    // 指令参数
    QString mInstructionText;
    CodeBooster::ModelParam mModelParam;

    // 文本补全需要的参数
    LanguageServerProtocol::Position mLspPosition;
    int mEditorPosition;

    // 其它
    QMap<QString, TextEditor::TextEditorWidget *> mChatWindowEditors;

    // 历史指令需要的参数
    int mHisoryInstCount;
    QStringList mInstHistory;
};


}
#endif // EDITORCHATWINDOW_H
