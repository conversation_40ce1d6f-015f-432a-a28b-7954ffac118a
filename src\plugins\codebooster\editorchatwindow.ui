<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EditorChatWindow</class>
 <widget class="QWidget" name="EditorChatWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>682</width>
    <height>116</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_action">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label_toggle">
       <property name="text">
        <string>Alt+K 切换</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_close">
       <property name="text">
        <string>Esc 关闭</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>2</number>
     </property>
     <property name="leftMargin">
      <number>1</number>
     </property>
     <property name="rightMargin">
      <number>1</number>
     </property>
     <item>
      <widget class="HistoryLineEdit" name="lineEdit_instruction"/>
     </item>
     <item>
      <widget class="QLabel" name="label_info">
       <property name="text">
        <string>TextLabel</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,1,2,2,2">
     <property name="leftMargin">
      <number>2</number>
     </property>
     <property name="topMargin">
      <number>2</number>
     </property>
     <property name="rightMargin">
      <number>2</number>
     </property>
     <property name="bottomMargin">
      <number>2</number>
     </property>
     <item>
      <widget class="QPushButton" name="pushButton_accept">
       <property name="text">
        <string>Ctrl+Enter 接受</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_reject">
       <property name="text">
        <string>Ctrl+Back 拒绝</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_cancel">
       <property name="text">
        <string>Ctrl+Back 取消</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QComboBox" name="comboBox_inst"/>
     </item>
     <item>
      <widget class="QComboBox" name="comboBox_model"/>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>HistoryLineEdit</class>
   <extends>QLineEdit</extends>
   <header>chatsidebar/historylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
