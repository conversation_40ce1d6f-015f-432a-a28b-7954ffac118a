#ifndef WIDGETTHEME_H
#define WIDGETTHEME_H

#define CB_THEME Theme::instance()

namespace CodeBooster::Internal{

class Theme
{
public:
    Theme();

public:
    static Theme &instance();

    QString SS_ChatBackground; ///< 对话控件背景样式表

    QString SS_MessagePreview_ToolBar; ///< 单挑对话工具栏样式表

    QString SS_MessagePreview;
    QString SS_MessagePreview_UserTextBrowser; ///< 用户消息预览控件中的文本显示控件
    QString SS_MessagePreview_AssistantMainTextBrowser;
    QString SS_MessagePreview_AssistantCodeTextBrowser;

    QString SS_MarkdownBlockWidget_CodeMode;
    QString SS_MarkdownBlockWidget_CodeMode_PreWgt;
    QString SS_MarkdownBlockWidget_CodeToolBar;
    QString SS_MarkdownBlockWidget_CodeToolBar_Highlight;
    QString SS_MarkdownBlockWidget_CodeToolBar_Label;

    QString SS_InputWidget_CodeSnippet;
    QString SS_EditorChatWindow;

    QString SS_InputWidget_ToolBar; ///< 直角工具栏样式

    QString Color_NomalBackground; ///< 文本块背景颜色
    QString Color_InputTextBackground; ///< 输入框背景颜色
    QString Color_MarkdownBlockWidget_CodeBackground; ///< 代码块背景颜色
    QString Color_MarkdownBlockWidget_CodeLine; ///< 代码块控件分隔线颜色

    QString Color_TextEdit_LineNumArea; ///< 文本编辑器行号区域背景颜色
    QString Color_TextEdit_NumberText; ///< 文本编辑器行号数字颜色
};

}
#endif // WIDGETTHEME_H
